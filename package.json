{"name": "collector-control", "private": true, "version": "0.0.0", "scripts": {"dev": "vite", "build": "vue-tsc --noEmit && vite build", "preview": "vite preview", "type-check": "vue-tsc --noEmit"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^0.27.2", "core-js": "^3.8.3", "echarts": "^5.6.0", "element-plus": "^2.2.9", "roslib": "^1.4.1", "vue": "^3.2.25", "vue-router": "^4.0.3"}, "devDependencies": {"@types/node": "^18.15.11", "@vitejs/plugin-vue": "^4.1.0", "@vue/cli-plugin-router": "~5.0.0", "sass": "^1.87.0", "typescript": "^5.0.2", "vite": "^4.3.0", "vue-tsc": "^2.2.10"}}