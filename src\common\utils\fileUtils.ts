/**
 * 文件相关的工具函数
 */

/**
 * 下载图像文件
 * @param base64Data 图像的base64数据
 * @param timestamp 用于文件名的时间戳
 */
export const downloadImage = (base64Data: string, timestamp: string): void => {
  const link = document.createElement('a');
  link.href = `data:image/jpeg;base64,${base64Data}`;
  link.download = `image_${timestamp.replace(/[: ]/g, '_')}.jpg`;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

/**
 * 下载二进制文件
 * @param base64Data 文件的base64数据
 * @param filename 文件名
 */
export const downloadBinary = (base64Data: string, filename: string): void => {
  const link = document.createElement('a');
  link.href = `data:application/octet-stream;base64,${base64Data}`;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

/**
 * 格式化文件大小
 * @param bytes 文件大小（字节）
 * @param decimals 小数点位数
 * @returns 格式化后的文件大小字符串
 */
export const formatBytes = (bytes: number, decimals = 2): string => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}; 