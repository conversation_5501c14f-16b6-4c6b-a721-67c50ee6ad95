import { request } from '@/common/request/request'

const baseUrl = import.meta.env.VITE_API_BASE_URL
/**
 * 创建用户
 * @param userData 用户数据
 */
function createUserApi (userData: any): Promise<any> {
  return request({
    method: 'POST',
      url: baseUrl + '/collector/users/',
    data: userData
  })
}
/**
 * 删除用户
 * @param userId 用户ID
 */
function deleteUserApi (userId: string): Promise<any> {
  return request({
    method: 'DELETE',
    url: `/collector/users/${userId}/`,
    data: {}
  })
}

/**
 * 获取用户列表
 */
function getUsersApi (): Promise<any> {
  return request({
    method: 'GET',
    url: baseUrl + '/collector/users/',
    headers: {
      'Accept': 'application/json'
    }
  })
}

/**
 * 更新用户
 * @param userId 用户ID
 * @param userData 用户数据
 */
function updateUserApi (userId: string, userData: any): Promise<any> {
  return request({
    method: 'PUT',
    url: baseUrl + `/collector/users/${userId}`,
    data: userData
  })
}

export {
  createUserApi,
  deleteUser<PERSON>pi,
  getUsersApi,
  updateUserApi
} 