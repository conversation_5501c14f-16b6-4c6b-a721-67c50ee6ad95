declare module 'roslib' {
  export class Ros {
    constructor(options: { url: string })
    on(event: 'connection' | 'error' | 'close', callback: (error?: Error) => void): void
    close(): void
  }

  export class Service {
    constructor(options: { ros: Ros; name: string; serviceType: string })
    callService(request: ServiceRequest, callback: (response: any) => void, errorCallback: (error: Error) => void): void
  }

  export class ServiceRequest {
    constructor(data?: any)
  }
} 