<script setup lang="ts">
import { RouterView, useRouter } from 'vue-router'

const router = useRouter()
</script>

<template>
  <div class="app-container">
    <div class="nav-menu" v-if="$route.path !== '/login' && $route.path !== '/' ">
      <div class="nav-item" @click="router.push('/')" :class="{ active: $route.path === '/' }">
        首页
      </div>
      <div class="nav-item" @click="router.push('/user-manage')" :class="{ active: $route.path === '/user-manage' }">
        用户管理
      </div>
      <div class="nav-item" @click="router.push('/requirement-manage')" :class="{ active: $route.path === '/requirement-manage' }">
        需求管理
      </div>
      <div class="nav-item" @click="router.push('/command-view')" :class="{ active: $route.path === '/command-view' }">
        指令管理
      </div>
      <div class="nav-item" @click="router.push('/task-manage')" :class="{ active: $route.path === '/task-manage' }">
        任务管理
      </div>
      <div class="nav-item" @click="router.push('/machine-manage')" :class="{ active: $route.path === '/machine-manage' }">
        设备管理
      </div>
      <div class="nav-item" @click="router.push('/detail-manage')" :class="{ active: $route.path === '/detail-manage' }">
        采集结果
      </div>
    </div>
    
    <RouterView />
  </div>
</template>

<style scoped>
.app-container {
  min-height: 100vh;
  background-color: #f5f7fa;
}

.nav-menu {
  display: flex;
  background-color: #ffffff;
  padding: 0 20px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
  border-bottom: 1px solid #e6e6e6;
}

.nav-item {
  padding: 15px 20px;
  color: #606266;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.3s;
  position: relative;
  margin-right: 10px;
}

.nav-item:hover {
  color: #303133;
  background-color: rgba(0, 0, 0, 0.02);
}

.nav-item.active {
  color: #303133;
  font-weight: 500;
}

.nav-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: #303133;
}
</style>


