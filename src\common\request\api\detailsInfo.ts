import { request } from '@/common/request/request'

const baseUrl = import.meta.env.VITE_API_BASE_URL

/**
 * 创建采集结果
 * @param detailData 采集结果数据
 */
function createDetailApi (detailData: any): Promise<any> {
  return request({
    method: 'POST',
    url: baseUrl + '/collector/details/',
    data: detailData
  })
}

/**
 * 删除采集结果
 * @param detailId 采集结果ID
 */
function deleteDetailApi (detailId: string): Promise<any> {
  return request({
    method: 'DELETE',
    url: baseUrl + `/collector/details/${detailId}/`,
    data: {}
  })
}

/**
 * 获取采集结果列表
 * @param params 查询参数，包含分页和搜索参数
 */
function getDetailsApi (params?: {
  current?: number
  size?: number
  description?: string
  requirement_title?: string
  command_description?: string
  executed_by_username?: string
  status?: string
  task_id?: string
}): Promise<any> {
  return request({
    method: 'GET',
    url: baseUrl + '/collector/details/',
    params: params,
    headers: {
      'Accept': 'application/json'
    }
  })
}

/**
 * 更新采集结果
 * @param detailId 采集结果ID
 * @param detailData 采集结果数据
 */
function updateDetailApi (detailId: string, detailData: any): Promise<any> {
  return request({
    method: 'PUT',
    url: baseUrl + `/collector/details/${detailId}/`,
    data: detailData
  })
}

export {
  createDetailApi,
  deleteDetailApi,
  getDetailsApi,
  updateDetailApi
} 