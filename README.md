# Lion Rock 数据收集控制系统 - 前端项目

**Lion Rock Data Collection System** 是一个专为机器人数据采集而设计的智能控制平台前端项目。基于 Vue 3 和现代化技术栈构建，提供直观的Web界面，支持多设备管理、实时数据监控和智能任务调度。

## 🎯 项目简介

本项目是Lion Rock数据收集控制系统的前端部分，旨在为机器人数据采集提供专业化的管控界面。系统支持：

- 🤖 **多设备管理**：同时连接和管理多台机器人设备
- 📡 **实时数据采集**：RGB相机、IMU传感器、位置跟踪等多维度数据
- 📊 **可视化监控**：实时图表、轨迹显示、传感器状态监控
- 🎯 **智能任务调度**：需求与指令灵活组合，自动化执行
- 🔧 **Web-ROS集成**：基于ROSLIB.js实现浏览器与ROS的直接通信

## 🚀 技术栈

### 核心框架
- **Vue.js** `3.2.25` - 响应式前端框架
- **TypeScript** `5.0.2` - 类型安全的JavaScript超集
- **Vite** `4.3.0` - 现代化构建工具

### UI & 样式
- **Element Plus** `2.2.9` - Vue 3组件库
- **SASS** `1.87.0` - CSS预处理器
- **Element Plus Icons** `2.3.1` - 图标库

### 路由 & 请求
- **Vue Router** `4.0.3` - 官方路由管理器
- **Axios** `0.27.2` - HTTP客户端

### 数据可视化 & ROS集成
- **ECharts** `5.6.0` - 数据可视化图表库
- **ROSLIB.js** `1.4.1` - ROS JavaScript库，实现WebSocket通信

### 开发工具
- **Vue TSC** `2.2.10` - TypeScript编译器
- **@vitejs/plugin-vue** `4.1.0` - Vue插件

## 📁 项目结构

```
collector_system_fontend/
├── public/                    # 静态资源目录
│   └── favicon.ico
├── src/                       # 源代码目录
│   ├── App.vue               # 根组件
│   ├── main.ts               # 入口文件
│   ├── env.d.ts              # 环境类型定义
│   ├── assets/               # 资源文件
│   │   └── logo.png
│   ├── common/               # 通用功能目录
│   │   ├── request/          # API请求模块
│   │   │   ├── api/          # API接口定义
│   │   │   └── request.ts    # 请求配置
│   │   └── utils/            # 工具函数
│   │       ├── error.ts      # 错误处理
│   │       ├── fileUtils.ts  # 文件操作
│   │       └── timeUtils.ts  # 时间处理
│   ├── components/           # 公共组件
│   ├── router/               # 路由配置
│   │   └── index.ts
│   ├── styles/               # 样式文件
│   │   └── scrollbar.css
│   ├── types/                # 类型定义
│   │   ├── roslib.d.ts       # ROS类型定义
│   │   └── router.d.ts       # 路由类型定义
│   └── views/                # 页面视图组件
│       ├── HomeView/         # 主页面（数据采集控制台）
│       ├── DetailManage/     # 采集结果管理
│       ├── MachineManage/    # 设备管理
│       ├── RequirementManage/ # 需求管理
│       ├── TaskManage/       # 任务管理
│       ├── CommandView/      # 指令管理
│       └── UserManageView/   # 用户管理
├── dist/                     # 构建输出目录
├── index.html               # HTML模板
├── package.json             # 项目依赖配置
├── tsconfig.json            # TypeScript配置
├── tsconfig.node.json       # Node.js TypeScript配置
├── vite.config.ts           # Vite配置
├── yarn.lock                # 依赖锁定文件
├── 01_项目概述.md           # 项目详细概述
├── 快速开始指南.md           # 快速开始指南
└── 简化操作流程.md           # 操作流程说明
```

## 🔧 核心功能模块

### 1. 数据采集控制台 (HomeView)
- **设备连接管理**：支持多设备同时连接，实时状态监控
- **传感器数据采集**：RGB相机、IMU传感器数据实时采集
- **可视化监控**：实时图表展示、轨迹绘制、传感器状态指示
- **采集任务控制**：开始/停止采集，实时状态跟踪
- **ROS集成**：WebSocket通信，支持ROS话题订阅和服务调用

### 2. 设备管理 (MachineManage)
- 设备信息管理（名称、IP地址、端口配置）
- 设备状态监控和连接测试
- 设备分组和批量操作

### 3. 需求管理 (RequirementManage)
- 采集场景需求定义（室内、室外等）
- 自定义需求创建和编辑
- 需求模板管理

### 4. 指令管理 (CommandView)
- 采集指令库管理
- 自定义指令创建和上传
- 指令参数配置

### 5. 任务管理 (TaskManage)
- 需求与指令组合形成完整任务
- 任务调度和执行监控
- 任务历史记录

### 6. 采集结果管理 (DetailManage)
- 采集数据记录查看
- 数据文件下载和管理
- 采集统计和分析

### 7. 用户管理 (UserManageView)
- 用户身份认证
- 操作权限管理
- 采集人员记录

## 🌐 ROS集成接口

### WebSocket通信
- **连接地址**：`ws://{设备IP}:9090`
- **协议**：ROS Bridge WebSocket

### 支持的ROS服务
- `/start_recording` - 开始录制服务
- `/stop_recording` - 停止录制服务  
- `/get_status` - 获取录制状态服务

### 支持的ROS话题
- `/go2_{machine_id}/imu` - IMU传感器数据
- `/go2_{machine_id}/camera/color/image_raw/compressed` - 压缩图像数据
- `/go2_{machine_id}/go2_states` - 机器人状态数据

### 支持的消息类型
- `sensor_msgs/Imu` - IMU传感器数据
- `sensor_msgs/CompressedImage` - 压缩图像数据  
- `go2_msgs/Go2State` - 机器人状态数据
- `rosbag_service/StartRecording` - 开始录制服务
- `std_srvs/Trigger` - 标准触发服务

## 🛠️ 开发环境设置

### 前提条件
- **Node.js** (v14.0+ 推荐)
- **Yarn** 或 **npm**
- **ROS环境**（用于后端通信）

### 安装依赖
```bash
# 使用 Yarn（推荐）
yarn install

# 或使用 npm
npm install
```

### 启动开发服务器
```bash
# 使用 Yarn
yarn dev

# 或使用 npm
npm run dev
```

应用将在 `http://localhost:5173` 运行

### 构建生产版本
```bash
# 使用 Yarn
yarn build

# 或使用 npm
npm run build
```

### 类型检查
```bash
# 使用 Yarn
yarn type-check

# 或使用 npm
npm run type-check
```

### 预览生产构建
```bash
# 使用 Yarn
yarn preview

# 或使用 npm
npm run preview
```

## 🚀 快速开始

### 基础配置
1. **启动开发服务器**
2. **访问系统地址**：`http://localhost:5173`
3. **输入用户名**登录系统
4. **添加设备**：设备管理 → 新建设备 → 填写IP和端口
5. **开始采集**：选择设备 → 选择需求和指令 → 开始采集

### 示例配置
```bash
用户名：test_user
设备IP：*************:7001
需求：室内场景采集
指令：标准采集模式
```

## 📊 核心操作流程

```
登录系统 → 设备管理 → 任务配置 → 开始采集 → 实时监控 → 停止采集 → 查看结果
```

### 详细流程
1. **登录系统** - 用户身份验证
2. **设备管理** - 添加和配置机器人设备
3. **任务配置** - 创建需求、指令和任务
4. **开始采集** - 启动数据采集任务
5. **实时监控** - 监控采集状态和数据流
6. **停止采集** - 结束采集并保存数据
7. **查看结果** - 查看和下载采集数据

## 🔧 推荐的IDE设置

- [VS Code](https://code.visualstudio.com/) + [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar)
- [WebStorm](https://www.jetbrains.com/webstorm/) (Vue 3支持)

### VS Code推荐插件
- Vue Language Features (Volar)
- TypeScript Vue Plugin (Volar)  
- ESLint
- Prettier

## 📝 项目版本

- **当前版本**：v1.0.0
- **发布日期**：2025年1月
- **代码名称**：Lion Rock
- **开发状态**：稳定版本

## 🌐 浏览器支持

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## 📄 相关文档

- [项目概述](./01_项目概述.md) - 详细的项目介绍和技术文档
- [快速开始指南](./快速开始指南.md) - 系统使用入门指南
- [操作流程说明](./简化操作流程.md) - 详细操作步骤说明

## 🚧 开发状态

- ✅ **核心功能**：已完成多设备管理、实时数据采集、任务调度
- ✅ **ROS集成**：已完成WebSocket通信、话题订阅、服务调用
- ✅ **用户界面**：已完成响应式设计、数据可视化、交互优化
- 🔄 **测试阶段**：进行用户验收测试和性能优化
- 📋 **文档完善**：持续更新技术文档和用户指南

---

*项目代号：Lion Rock*  
*最后更新：2025年1月*
