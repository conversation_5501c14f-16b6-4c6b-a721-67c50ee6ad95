import { request } from '@/common/request/request'

const baseUrl = import.meta.env.VITE_API_BASE_URL

/**
 * 创建任务
 * @param taskData 任务数据
 */
function createTaskApi (taskData: any): Promise<any> {
  return request({
    method: 'POST',
    url: baseUrl + '/collector/tasks/',
    data: taskData
  })
}

/**
 * 删除任务
 * @param taskId 任务ID
 */
function deleteTaskApi (taskId: string): Promise<any> {
  return request({
    method: 'DELETE',
    url: baseUrl + `/collector/tasks/${taskId}/`,
    data: {}
  })
}

/**
 * 获取任务列表
 * @param params 查询参数，可选
 */
function getTasksApi (params?: { requirement_id?: string | number, command_id?: string | number }): Promise<any> {
  let url = baseUrl + '/collector/tasks/'
  
  // 如果有查询参数，构建查询字符串
  if (params) {
    const searchParams = new URLSearchParams()
    if (params.requirement_id) {
      searchParams.append('requirement_id', params.requirement_id.toString())
    }
    if (params.command_id) {
      searchParams.append('command_id', params.command_id.toString())
    }
    if (searchParams.toString()) {
      url += '?' + searchParams.toString()
    }
  }
  
  return request({
    method: 'GET',
    url: url,
    headers: {
      'Accept': 'application/json'
    }
  })
}

/**
 * 更新任务
 * @param taskId 任务ID
 * @param taskData 任务数据
 */
function updateTaskApi (taskId: string, taskData: any): Promise<any> {
  return request({
    method: 'PUT',
    url: baseUrl + `/collector/tasks/${taskId}/`,
    data: taskData
  })
}

export {
  createTaskApi,
  deleteTaskApi,
  getTasksApi,
  updateTaskApi
} 