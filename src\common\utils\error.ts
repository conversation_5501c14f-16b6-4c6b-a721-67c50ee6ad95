export type ErrorContent = {
  type: 'bizError' | 'netError'
  code: string | null,
  message: string
}

export function setError (params: ErrorContent): Error {
  return Error(JSON.stringify(params))
}

export function getError (error: Error): ErrorContent {
  return JSON.parse(error.toString().replace(/^Error: /, ''))
}

/**
 * 获取错误提示信息
 * @param err
 * @param msg
 * @returns
 */
export function getErrorMessage (err: Error, msg: string): string {
  let errMsg = ''
  try {
    errMsg = JSON.parse(err.message).message || msg
  } catch (error) {
    errMsg = msg
  }
  return errMsg
}
