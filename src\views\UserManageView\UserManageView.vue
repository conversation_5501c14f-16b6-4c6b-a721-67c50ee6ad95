<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { createUserApi, getUsersApi, updateUserApi, deleteUserApi } from '@/common/request/api/userInfo'

// 用户列表
const userList = ref<any[]>([])

// 分页信息
const pagination = ref({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 加载状态
const loading = ref(false)

// 搜索关键词
const searchKeyword = ref('')

// 用户表单
const userForm = ref({
  id: '',
  username: '',
  employee_id: '',
  department: '',
  position: '',
  contact: ''
})

// 表单对话框状态
const dialogVisible = ref(false)
const dialogTitle = ref('新建用户')
const dialogType = ref('create') // 'create' 或 'edit'

// 表单引用
const userFormRef = ref<any>(null)

// 表单验证规则
const rules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  department: [
    { max: 50, message: '部门名称不能超过50个字符', trigger: 'blur' }
  ],
  position: [
    { max: 50, message: '职位名称不能超过50个字符', trigger: 'blur' }
  ],
  contact: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ]
}

// 重置表单
const resetForm = () => {
  userForm.value = {
    id: '',
    username: '',
    employee_id: '',
    department: '',
    position: '',
    contact: ''
  }
}

// 获取用户列表
const fetchUsers = async () => {
  try {
    loading.value = true
    
    try {
      console.log('正在请求用户列表...')
      const res = await getUsersApi()
      console.log('用户列表数据:', res)
      
      if (Array.isArray(res.data)) {
        // 确保每个用户数据都有id字段
        userList.value = res.data.map((user: any) => {
          // 如果没有id字段，可以暂时使用username作为临时id
          if (!user.id && user.username) {
            console.warn(`用户 ${user.username} 缺少id字段，使用临时id`)
            return { ...user, id: `temp_${user.username}` }
          }
          return user
        })
        pagination.value.total = res.data.length
        console.log('获取到的用户列表:', userList.value)
      } else {
        throw new Error('接口返回数据格式异常')
      }
      
      // 分页处理
      // 如果后端支持分页，应该使用后端返回的分页数据
      // 这里先用前端分页作为示例
      const startIndex = (pagination.value.currentPage - 1) * pagination.value.pageSize
      const endIndex = startIndex + pagination.value.pageSize
      userList.value = userList.value.slice(startIndex, endIndex)
      
    } catch (apiError: any) {
      console.error('API调用失败:', apiError)
      
      // 特别处理500错误
      if (apiError.response && apiError.response.status === 500) {
        ElMessage.error('服务器内部错误(500)，请联系后端开发人员检查服务器日志')
        console.error('服务器500错误详情:', apiError.response.data)
      } else {
        ElMessage.error(`API调用失败: ${apiError.message || '未知错误'}`)
      }
    }
  } catch (error: any) {
    console.error('获取用户列表失败:', error)
    ElMessage.error(`获取用户列表失败: ${error.message || '未知错误'}`)
  } finally {
    loading.value = false
  }
}

// 打开新建用户对话框
const openCreateDialog = () => {
  resetForm()
  dialogType.value = 'create'
  dialogTitle.value = '新建用户'
  dialogVisible.value = true
  
  nextTick(() => {
    if (userFormRef.value) {
      userFormRef.value.clearValidate()
    }
  })
}

// 打开编辑用户对话框
const openEditDialog = (row: any) => {
  resetForm()
  dialogType.value = 'edit'
  dialogTitle.value = '编辑用户'
  
  // 填充表单数据
  if (row.id) userForm.value.id = row.id;
  if (row.username) userForm.value.username = row.username;
  if (row.employee_id) userForm.value.employee_id = row.employee_id;
  if (row.department) userForm.value.department = row.department;
  if (row.position) userForm.value.position = row.position;
  if (row.contact) userForm.value.contact = row.contact;
  
  // 如果没有id字段，给出警告
  if (!row.id) {
    console.warn('编辑的用户数据缺少id字段，可能导致更新操作失败')
    ElMessage.warning('用户数据缺少ID，编辑功能可能受限')
  }
  
  dialogVisible.value = true
  
  nextTick(() => {
    if (userFormRef.value) {
      userFormRef.value.clearValidate()
    }
  })
}

// 提交用户表单
const submitForm = async () => {
  if (!userFormRef.value) return
  
  try {
    await userFormRef.value.validate()
    
    if (dialogType.value === 'create') {
      // 创建用户
      await createUserApi(userForm.value)
      ElMessage.success('用户创建成功')
    } else {
      // 更新用户
      await updateUserApi(userForm.value.id, userForm.value)
      ElMessage.success('用户更新成功')
    }
    
    dialogVisible.value = false
    fetchUsers() // 刷新列表
  } catch (error: any) {
    console.error('表单提交失败:', error)
    ElMessage.error(`操作失败: ${error.message || '表单验证失败，请检查输入'}`)
  }
}

// 删除用户
const handleDelete = async (id: string | undefined) => {
  if (!id) {
    ElMessage.warning('用户数据缺少ID，无法删除')
    return
  }
  
  try {
    await ElMessageBox.confirm('确定要删除该用户吗？此操作不可逆', '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    try {
      // 调用删除API
      const res = await deleteUserApi(id)
      console.log('删除响应:', res)
      
      // 删除成功，刷新列表
      ElMessage.success('用户删除成功')
      fetchUsers()
    } catch (apiError: any) {
      console.error('API错误:', apiError)
      
      // 尝试解析错误信息
      try {
        if (typeof apiError.message === 'string' && apiError.message.includes('bizError')) {
          const errorObj = JSON.parse(apiError.message)
          // 如果错误消息中包含"删除成功"，则视为成功
          if (errorObj.message && errorObj.message.includes('删除成功')) {
            ElMessage.success(errorObj.message)
            fetchUsers() // 刷新列表
            return
          }
        }
      } catch (parseError) {
        console.error('解析错误信息失败:', parseError)
      }
      
      // 其他错误情况
      ElMessage.error(`删除用户失败: ${apiError.message || '未知错误'}`)
    }
  } catch (error: any) {
    // 用户取消操作，不做处理
    if (error === 'cancel') {
      return
    }
    console.error('操作错误:', error)
  }
}

// 处理分页变化
const handleCurrentChange = (page: number) => {
  pagination.value.currentPage = page
  fetchUsers()
}

// 处理每页显示条数变化
const handleSizeChange = (size: number) => {
  pagination.value.pageSize = size
  pagination.value.currentPage = 1
  fetchUsers()
}

// 处理搜索
const handleSearch = () => {
  pagination.value.currentPage = 1
  fetchUsers()
}

// 页面加载时获取用户列表
onMounted(() => {
  fetchUsers()
})
</script>

<template>
  <div class="user-manage-container">
    <div class="page-header">
      <div class="breadcrumb">
        <span>您当前的位置：</span>
        <span class="location">数据采集系统</span>
        <span class="separator">/</span>
        <span class="current">用户管理</span>
      </div>
    </div>
    
    <div class="page-title">
      <h1>用户管理</h1>
    </div>
    
    <div class="page-content">
      <!-- 操作栏 -->
      <div class="operation-bar">
        <div class="left-actions">
          <el-button type="primary" @click="openCreateDialog">
            新建用户
          </el-button>
        </div>
        
        <div class="right-actions">
          <el-input
            v-model="searchKeyword"
            placeholder="请输入用户名或姓名"
            clearable
            @keyup.enter="handleSearch"
            class="search-input"
          >
            <template #append>
              <el-button @click="handleSearch">
                搜索
              </el-button>
            </template>
          </el-input>
        </div>
      </div>
      
      <!-- 用户表格 -->
      <el-table
        :data="userList"
        style="width: 100%"
        border
        v-loading="loading"
        max-height="500"
      >
        <el-table-column
          type="index"
          label="序号"
          width="80"
          align="center"
        />
        
        <el-table-column
          prop="username"
          label="用户名"
          min-width="120"
        >
          <template #default="scope">
            {{ scope.row.username || '暂无' }}
          </template>
        </el-table-column>
        
        <el-table-column
          prop="employee_id"
          label="工号"
          min-width="120"
        >
          <template #default="scope">
            {{ scope.row.employee_id || '暂无' }}
          </template>
        </el-table-column>
        
        <el-table-column
          prop="department"
          label="部门"
          min-width="120"
        >
          <template #default="scope">
            {{ scope.row.department || '暂无' }}
          </template>
        </el-table-column>
        
        <el-table-column
          prop="position"
          label="职位"
          min-width="120"
        >
          <template #default="scope">
            {{ scope.row.position || '暂无' }}
          </template>
        </el-table-column>
        
        <el-table-column
          prop="contact"
          label="联系方式"
          min-width="120"
        >
          <template #default="scope">
            {{ scope.row.contact || '暂无' }}
          </template>
        </el-table-column>
        
        <el-table-column
          prop="created_at"
          label="创建时间"
          min-width="180"
        >
          <template #default="scope">
            {{ scope.row.created_at || '暂无' }}
          </template>
        </el-table-column>
        
        <el-table-column
          label="操作"
          width="150"
          fixed="right"
        >
          <template #default="scope">
            <el-button
              type="primary"
              link
              @click="openEditDialog(scope.row)"
            >
              编辑
            </el-button>
            <el-button
              type="danger"
              link
              @click="handleDelete(scope.row.id)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          background
        />
      </div>
    </div>
    
    <!-- 用户表单对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="500px"
      destroy-on-close
    >
      <el-form
        ref="userFormRef"
        :model="userForm"
        :rules="rules"
        label-width="80px"
        label-position="right"
      >
        <el-form-item label="用户名" prop="username">
          <el-input v-model="userForm.username" placeholder="请输入用户名" />
        </el-form-item>
        
        <el-form-item label="工号" prop="employee_id" v-if="dialogType === 'edit'">
          <el-input v-model="userForm.employee_id" placeholder="系统自动生成" disabled />
        </el-form-item>
        
        <el-form-item label="部门" prop="department">
          <el-input v-model="userForm.department" placeholder="请输入部门" />
        </el-form-item>
        
        <el-form-item label="职位" prop="position">
          <el-input v-model="userForm.position" placeholder="请输入职位" />
        </el-form-item>
        
        <el-form-item label="联系方式" prop="contact">
          <el-input v-model="userForm.contact" placeholder="请输入手机号码" />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.user-manage-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.breadcrumb {
  font-size: 14px;
  color: #606266;
  
  .location {
    color: #909399;
  }
  
  .separator {
    margin: 0 8px;
    color: #C0C4CC;
  }
  
  .current {
    color: #303133;
    font-weight: 500;
  }
}

.page-title {
  margin-bottom: 20px;
  
  h1 {
    font-size: 24px;
    color: #303133;
    margin: 0;
    font-weight: 600;
  }
}

.page-content {
  background-color: #fff;
  border-radius: 4px;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.operation-bar {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  
  .search-input {
    width: 300px;
  }
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style> 