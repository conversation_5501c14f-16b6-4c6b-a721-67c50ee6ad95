import { ref, reactive } from 'vue'

// 用户管理的状态和逻辑
function useUserManage() {
  // 用户列表
  const userList = ref<any[]>([])
  
  // 分页信息
  const pagination = reactive({
    currentPage: 1,
    pageSize: 10,
    total: 0
  })
  
  // 加载状态
  const loading = ref(false)
  
  // 搜索关键词
  const searchKeyword = ref('')
  
  // 用户表单
  const userForm = reactive({
    id: '',
    username: '',
    realName: '',
    email: '',
    phone: '',
    role: '',
    status: 1
  })
  
  // 表单对话框状态
  const dialogVisible = ref(false)
  const dialogTitle = ref('新建用户')
  const dialogType = ref('create') // 'create' 或 'edit'
  
  // 用户角色选项
  const roleOptions = [
    { label: '管理员', value: 'admin' },
    { label: '操作员', value: 'operator' },
    { label: '访客', value: 'visitor' }
  ]
  
  // 用户状态选项
  const statusOptions = [
    { label: '启用', value: 1 },
    { label: '禁用', value: 0 }
  ]
  
  // 重置表单
  const resetForm = () => {
    userForm.id = ''
    userForm.username = ''
    userForm.realName = ''
    userForm.email = ''
    userForm.phone = ''
    userForm.role = ''
    userForm.status = 1
  }

  return {
    userList,
    pagination,
    loading,
    searchKeyword,
    userForm,
    dialogVisible,
    dialogTitle,
    dialogType,
    roleOptions,
    statusOptions,
    resetForm
  }
}

export default useUserManage 