/// <reference types="vite/client" />

// 腾讯 POC SDK 类型声明
declare global {
  interface Window {
    TRROSDK: any;
  }
}

// TRRO SDK 类型定义
interface TRROSDKParams {
  cloudMode?: 'public' | 'private';
  serverIp?: string | string[];
  projectId?: string | number;
  remoteDeviceId?: string;
  password?: string;
  sdkAppId?: string | number;
  roomId?: string | number;
  userId?: string;
  userSig?: string;
}

declare class TRROSDK {
  constructor(params: TRROSDKParams);
  // 这里可以根据实际 SDK 的方法添加更多类型定义
}

export { TRROSDK, TRROSDKParams };

declare module '*.vue' {
  import type { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

declare module 'element-plus' 