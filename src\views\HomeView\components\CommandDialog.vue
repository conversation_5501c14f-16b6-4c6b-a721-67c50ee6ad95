<script setup lang="ts">
import { defineProps, defineEmits } from 'vue'
import { ElButton, ElDialog, ElRadio } from 'element-plus'
import { ref, onMounted } from 'vue'
import { getCommandsApi } from '@/common/request/api/commandInfo'

// 指令列表
const commandList = ref<any[]>([])
// 分页信息
const pagination = ref({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 加载状态
const loading = ref(false)
const props = defineProps({
  visible: {
    type: Boolean,
    required: true
  },
  title: {
    type: String,
    default: '操作'
  }
})
console.log(props.visible)
const emits = defineEmits(['update:visible', 'confirm', 'cancel'])

// 新增：保存选中的指令id
const selectedCommandId = ref<string | null>(null)

// 新增：选中行变化时
function handleSelectRow(row: any) {
  selectedCommandId.value = row.id
}

function handleCancel() {
  emits('update:visible', false)
  emits('cancel')
}

function handleConfirm() {
  emits('confirm', selectedCommandId.value)
  emits('update:visible', false)
}

// 获取指令列表
const fetchCommands = async () => {
  try {
    loading.value = true
    
    try {
      console.log('正在请求指令列表...')
      const res = await getCommandsApi()
      console.log('指令列表数据:', res)
      
      if (Array.isArray(res.data)) {
        // 确保每个指令数据都有id字段
        commandList.value = res.data.map((command: any) => {
          // 如果没有id字段，可以暂时使用临时id
          if (!command.id) {
            console.warn(`指令缺少id字段，使用临时id`)
            return { ...command, id: `temp_${Math.random().toString(36).substring(2, 9)}` }
          }
          return command
        })
        pagination.value.total = res.data.length
      } else {
        throw new Error('接口返回数据格式异常')
      }
      
      // 分页处理
      const startIndex = (pagination.value.currentPage - 1) * pagination.value.pageSize
      const endIndex = startIndex + pagination.value.pageSize
      commandList.value = commandList.value.slice(startIndex, endIndex)
      
    } catch (apiError: any) {
      console.error('API调用失败:', apiError)
      
      // 特别处理500错误
      if (apiError.response && apiError.response.status === 500) {
        console.error('服务器500错误详情:', apiError.response.data)
      } else {
      }
    }
  } catch (error: any) {
    console.error('获取指令列表失败:', error)
  } finally {
    loading.value = false
  }
}
// 处理分页变化
const handleCurrentChange = (page: number) => {
  pagination.value.currentPage = page
  fetchCommands()
}

// 处理每页显示条数变化
const handleSizeChange = (size: number) => {
  pagination.value.pageSize = size
  pagination.value.currentPage = 1
  fetchCommands()
}
// 页面加载时获取指令列表
onMounted(() => {
  fetchCommands()
})
</script>

<template>
  <el-dialog
    :model-value="visible"
    :title="title"
    width="1000px"
    @close="handleCancel"
    :close-on-click-modal="false"
  >
    <!-- <slot /> -->
    <el-table
        :data="commandList"
        style="width: 100%"
        border
        :loading="loading"
      >
        
        <el-table-column
          width="100"
          align="center"
        >
          <template #default="scope">
            <el-radio
              :model-value="selectedCommandId"
              @change="() => handleSelectRow(scope.row)"
            />
          </template>
        </el-table-column>
        
        <el-table-column
          prop="id"
          label="指令ID"
          min-width="100"
        >
          <template #default="scope">
            {{ scope.row.id || '暂无' }}
          </template>
        </el-table-column>
        
        <el-table-column
          prop="description"
          label="指令描述"
          min-width="200"
          show-overflow-tooltip
        >
          <template #default="scope">
            {{ scope.row.description || '暂无' }}
          </template>
        </el-table-column>
        
        <el-table-column
          prop="created_by_username"
          label="创建人"
          min-width="100"
        >
          <template #default="scope">
            {{ scope.row.created_by_username || '暂无' }}
          </template>
        </el-table-column>
        
        <el-table-column
          prop="created_at"
          label="创建时间"
          min-width="160"
        >
          <template #default="scope">
            {{ scope.row.created_at || '暂无' }}
          </template>
        </el-table-column>
        
        <el-table-column
          prop="updated_at"
          label="更新时间"
          min-width="160"
        >
          <template #default="scope">
            {{ scope.row.updated_at || '暂无' }}
          </template>
        </el-table-column>
      </el-table>
            <!-- 分页 -->
    <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          background
        />
      </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>



<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
.page-title {
  margin-bottom: 20px;
  
  h1 {
    font-size: 24px;
    color: #303133;
    margin: 0;
    font-weight: 600;
  }
}

.page-content {
  background-color: #fff;
  border-radius: 4px;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

</style>
