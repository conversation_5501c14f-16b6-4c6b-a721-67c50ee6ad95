/* eslint-disable @typescript-eslint/no-explicit-any */
import { setError } from '@/common/utils/error'
import axios from 'axios'
import type { AxiosRequestConfig } from 'axios'

const $axios = axios.create({
  timeout: 15000, // 15秒超时
  headers: {
    'Content-Type': 'application/json'
  }
})

$axios.interceptors.response.use(response => {
  return response
}, error => {
  console.error('axios拦截器捕获到错误:', error)
  
  if (error.response) {
    // 服务器返回了错误状态码
    console.error('响应错误:', {
      status: error.response.status,
      data: error.response.data,
      headers: error.response.headers
    })
    
    if (error.response.status === 401) {
      // todo: 处理未授权错误
    }
  } else if (error.request) {
    // 请求发出但没有收到响应
    console.error('请求无响应:', error.request)
  } else {
    // 其他错误
    console.error('请求设置错误:', error.message)
  }
  
  return Promise.reject(error)
})

/**
 * 请求的包装方法
 *
 * @params options.method string
 * @params options.url string
 */
function request<T> (options: AxiosRequestConfig): Promise<T> {
  return new Promise((resolve, reject) => {
    $axios(options).then(data => {
      // 检查响应数据是否存在
      if (!data) {
        reject(Error('响应数据为空'))
        return
      }
      
      if (data.status === 200) {
        if (data.data && data.data.code === '0') {
          resolve(data.data.data)
        } else if (data.data && data.data.message) {
          reject(setError({
            type: 'bizError',
            code: data.data.code,
            message: data.data.message
          }))
        } else {
          // 返回字节流
          resolve(data.data as unknown as T)
        }
      } else {
        reject(Error(`请求失败，状态码: ${data.status}`))
      }
    }).catch(err => {
      // 添加更详细的错误信息
      console.error('请求发生错误:', err)
      reject(err)
    })
  })
}

request.get = <T>(url: string, config?: AxiosRequestConfig): Promise<T> => {
  let options: AxiosRequestConfig = {}
  if (config) options = { ...config }

  return request({ url, ...options })
}
request.post = <T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {
  let options: AxiosRequestConfig = {}
  if (data) options = { data }
  if (config) options = { ...options, ...config }

  return request({ url, ...options })
}

export default $axios

export {
  request
}
