// 腾讯 TRRO SDK 类型声明文件

// TRRO SDK 参数接口
export interface TRROSDKParams {
  cloudMode?: 'public' | 'private';
  serverIp?: string | string[];
  projectId?: string | number;
  remoteDeviceId?: string;
  password?: string;
  sdkAppId?: string | number;
  roomId?: string | number;
  userId?: string;
  userSig?: string;
}

// TRRO SDK 初始化结果接口
export interface TRROSDKInitResult {
  code: number;
  message: string;
}

// TRRO SDK 类声明
export declare class TRROSDK {
  constructor(params: TRROSDKParams);

  // 初始化 MQTT 连接
  init(): Promise<TRROSDKInitResult>;
  connect(fieldDeviceId:string): Promise<TRROSDKInitResult>;
  // disconnect(): void;
  // sendData(data: any): void;
  // on(event: string, callback: Function): void;
  // off(event: string, callback?: Function): void;
}

// 全局声明
declare global {
  interface Window {
    TRROSDK: typeof TRROSDK;
    globalTRROInstance: TRROSDK | null;
  }
}

// Vue 全局属性声明
declare module '@vue/runtime-core' {
  interface ComponentCustomProperties {
    $TRROSDK: typeof TRROSDK | null;
    $createTRROSDKInstance: (params: TRROSDKParams) => TRROSDK;
  }
}
