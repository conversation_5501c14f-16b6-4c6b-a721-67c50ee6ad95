// 腾讯 TRRO SDK 类型声明文件

// TRRO SDK 参数接口
export interface TRROSDKParams {
  cloudMode?: 'public' | 'private';
  serverIp?: string | string[];
  projectId?: string | number;
  remoteDeviceId?: string;
  password?: string;
  sdkAppId?: string | number;
  roomId?: string | number;
  userId?: string;
  userSig?: string;
}

// TRRO SDK 类声明
export declare class TRROSDK {
  constructor(params: TRROSDKParams);
  
  // 这里可以根据实际 SDK 的方法添加更多类型定义
  // 例如：
  // connect(): Promise<void>;
  // disconnect(): void;
  // sendData(data: any): void;
  // on(event: string, callback: Function): void;
  // off(event: string, callback?: Function): void;
}

// 全局声明
declare global {
  interface Window {
    TRROSDK: typeof TRROSDK;
  }
}

// Vue 全局属性声明
declare module '@vue/runtime-core' {
  interface ComponentCustomProperties {
    $TRROSDK: typeof TRROSDK | null;
    $createTRROSDKInstance: (params: TRROSDKParams) => TRROSDK;
  }
}
