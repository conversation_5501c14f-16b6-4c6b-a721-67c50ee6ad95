<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox, ElDatePicker, ElSelect, ElOption, ElTag } from 'element-plus'
import { createRequirementApi, getRequirementsApi, updateRequirementApi, deleteRequirementApi } from '@/common/request/api/requirementInfo'

// 需求列表
const requirementList = ref<any[]>([])

// 分页信息
const pagination = ref({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 加载状态
const loading = ref(false)

// 搜索关键词
const searchKeyword = ref('')
const storedCollectorName = localStorage.getItem('collectorName')

// 需求表单
const requirementForm = ref({
  id: '',
  title: '',
  description: '',
  proposer: storedCollectorName,
  proposer_id: '',
  priority: 3,
  status: 'pending',
  deadline: ''
})

// 表单对话框状态
const dialogVisible = ref(false)
const dialogTitle = ref('新建需求')
const dialogType = ref('create') // 'create' 或 'edit'

// 表单引用
const requirementFormRef = ref<any>(null)

// 需求状态选项
const statusOptions = [
  { label: '待处理', value: 'pending' },
  { label: '采集中', value: 'ongoing' },
  { label: '已完成', value: 'completed' },
  { label: '已取消', value: 'canceled' }
]

// 优先级选项
const priorityOptions = [
  { label: '低', value: 1 },
  { label: '中', value: 2 },
  { label: '高', value: 3 }
]

// 表单验证规则
const rules = {
  title: [
    { required: true, message: '请输入需求名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入需求描述', trigger: 'blur' }
  ]
}

// 重置表单
const resetForm = () => {
  requirementForm.value = {
    id: '',
    title: '',
    description: '',
    proposer: storedCollectorName || '',
    proposer_id: '',
    priority: 3,
    status: 'pending',
    deadline: ''
  }
}

// 获取需求列表
const fetchRequirements = async () => {
  try {
    loading.value = true
    
    try {
      console.log('正在请求需求列表...')
      const res = await getRequirementsApi()
      console.log('需求列表数据:', res)
      
      if (Array.isArray(res.data)) {
        // 确保每个需求数据都有id字段
        requirementList.value = res.data.map((requirement: any) => {
          // 如果没有id字段，可以暂时使用name作为临时id
          if (!requirement.id && requirement.title) {
            console.warn(`需求 ${requirement.title} 缺少id字段，使用临时id`)
            return { ...requirement, id: `temp_${requirement.title}` }
          }
          return requirement
        })
        pagination.value.total = res.data.length
        console.log('获取到的需求列表:', requirementList.value)
      } else {
        throw new Error('接口返回数据格式异常')
      }
      
      // 分页处理
      const startIndex = (pagination.value.currentPage - 1) * pagination.value.pageSize
      const endIndex = startIndex + pagination.value.pageSize
      requirementList.value = requirementList.value.slice(startIndex, endIndex)
      
    } catch (apiError: any) {
      console.error('API调用失败:', apiError)
      
      // 特别处理500错误
      if (apiError.response && apiError.response.status === 500) {
        ElMessage.error('服务器内部错误(500)，请联系后端开发人员检查服务器日志')
        console.error('服务器500错误详情:', apiError.response.data)
      } else {
        ElMessage.error(`API调用失败: ${apiError.message || '未知错误'}`)
      }
    }
  } catch (error: any) {
    console.error('获取需求列表失败:', error)
    ElMessage.error(`获取需求列表失败: ${error.message || '未知错误'}`)
  } finally {
    loading.value = false
  }
}

// 打开新建需求对话框
const openCreateDialog = () => {
  resetForm()
  // 设置创建人为当前登录用户
  requirementForm.value.proposer = storedCollectorName || ''
  dialogType.value = 'create'
  dialogTitle.value = '新建需求'
  dialogVisible.value = true
  
  nextTick(() => {
    if (requirementFormRef.value) {
      requirementFormRef.value.clearValidate()
    }
  })
}

// 打开编辑需求对话框
const openEditDialog = (row: any) => {
  resetForm()
  dialogType.value = 'edit'
  dialogTitle.value = '编辑需求'
  
  // 填充表单数据
  if (row.id) requirementForm.value.id = row.id;
  if (row.title) requirementForm.value.title = row.title;
  if (row.description) requirementForm.value.description = row.description;
  if (row.proposer) requirementForm.value.proposer = row.proposer;
  if (row.proposer_id) requirementForm.value.proposer_id = row.proposer_id;
  if (row.priority) requirementForm.value.priority = row.priority;
  if (row.status) requirementForm.value.status = row.status;
  if (row.deadline) requirementForm.value.deadline = row.deadline;
  
  // 如果没有id字段，给出警告
  if (!row.id) {
    console.warn('编辑的需求数据缺少id字段，可能导致更新操作失败')
    ElMessage.warning('需求数据缺少ID，编辑功能可能受限')
  }
  
  dialogVisible.value = true
  
  nextTick(() => {
    if (requirementFormRef.value) {
      requirementFormRef.value.clearValidate()
    }
  })
}

// 提交需求表单
const submitForm = async () => {
  if (!requirementFormRef.value) return
  
  try {
    await requirementFormRef.value.validate()
    
    if (dialogType.value === 'create') {
      // 创建需求时只传递必要字段
      const createData = {
        title: requirementForm.value.title,
        description: requirementForm.value.description,
        proposer: requirementForm.value.proposer,
        priority: requirementForm.value.priority,
        status: requirementForm.value.status,
        deadline: requirementForm.value.deadline
      }
      // 创建需求
      await createRequirementApi(createData)
      ElMessage.success('需求创建成功')
    } else {
      // 更新需求时只传递必要字段，不包括proposer_id
      const updateData = {
        id: requirementForm.value.id,
        title: requirementForm.value.title,
        description: requirementForm.value.description,
        proposer: requirementForm.value.proposer,
        priority: requirementForm.value.priority,
        status: requirementForm.value.status,
        deadline: requirementForm.value.deadline
      }
      // 更新需求
      await updateRequirementApi(requirementForm.value.id, updateData)
      ElMessage.success('需求更新成功')
    }
    
    dialogVisible.value = false
    fetchRequirements() // 刷新列表
  } catch (error: any) {
    console.error('表单提交失败:', error)
    ElMessage.error(`操作失败: ${error.message || '表单验证失败，请检查输入'}`)
  }
}

// 删除需求
const handleDelete = async (id: string | undefined) => {
  if (!id) {
    ElMessage.warning('需求数据缺少ID，无法删除')
    return
  }
  
  try {
    await ElMessageBox.confirm('确定要删除该需求吗？此操作不可逆，同时会删除该需求对应的所有任务', '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    try {
      // 调用删除API
      const res = await deleteRequirementApi(id)
      console.log('删除响应:', res)
      
      // 删除成功，刷新列表
      ElMessage.success('需求删除成功')
      fetchRequirements()
    } catch (apiError: any) {
      console.error('API错误:', apiError)
      
      // 尝试解析错误信息
      try {
        if (typeof apiError.message === 'string' && apiError.message.includes('bizError')) {
          const errorObj = JSON.parse(apiError.message)
          // 如果错误消息中包含"删除成功"，则视为成功
          if (errorObj.message && errorObj.message.includes('删除成功')) {
            ElMessage.success(errorObj.message)
            fetchRequirements() // 刷新列表
            return
          }
        }
      } catch (parseError) {
        console.error('解析错误信息失败:', parseError)
      }
      
      // 其他错误情况
      ElMessage.error(`删除需求失败: ${apiError.message || '未知错误'}`)
    }
  } catch (error: any) {
    // 用户取消操作，不做处理
    if (error === 'cancel') {
      return
    }
    console.error('操作错误:', error)
  }
}

// 处理分页变化
const handleCurrentChange = (page: number) => {
  pagination.value.currentPage = page
  fetchRequirements()
}

// 处理每页显示条数变化
const handleSizeChange = (size: number) => {
  pagination.value.pageSize = size
  pagination.value.currentPage = 1
  fetchRequirements()
}

// 处理搜索
const handleSearch = () => {
  pagination.value.currentPage = 1
  fetchRequirements()
}

// 页面加载时获取需求列表
onMounted(() => {
  fetchRequirements()
})
</script>

<template>
  <div class="requirement-manage-container">
    <div class="page-header">
      <div class="breadcrumb">
        <span>您当前的位置：</span>
        <span class="location">数据采集系统</span>
        <span class="separator">/</span>
        <span class="current">需求管理</span>
      </div>
    </div>
    
    <div class="page-title">
      <h1>需求管理</h1>
    </div>
    
    <div class="page-content">
      <!-- 操作栏 -->
      <div class="operation-bar">
        <div class="left-actions">
          <el-button type="primary" @click="openCreateDialog">
            新建需求
          </el-button>
        </div>
        
        <div class="right-actions">
          <el-input
            v-model="searchKeyword"
            placeholder="请输入需求名称或ID"
            clearable
            @keyup.enter="handleSearch"
            class="search-input"
          >
            <template #append>
              <el-button @click="handleSearch">
                搜索
              </el-button>
            </template>
          </el-input>
        </div>
      </div>
      
      <!-- 需求表格 -->
      <el-table
        :data="requirementList"
        style="width: 100%"
        border
        :loading="loading"
        max-height="500"
      >
        <el-table-column
          type="index"
          label="序号"
          width="80"
          align="center"
        />
        
        <el-table-column
          prop="id"
          label="需求ID"
          min-width="100"
        >
          <template #default="scope">
            {{ scope.row.id || '暂无' }}
          </template>
        </el-table-column>
        
        <el-table-column
          prop="title"
          label="需求名称"
          min-width="150"
        >
          <template #default="scope">
            {{ scope.row.title || '暂无' }}
          </template>
        </el-table-column>
        
        <el-table-column
          prop="description"
          label="需求描述"
          min-width="200"
          show-overflow-tooltip
        >
          <template #default="scope">
            {{ scope.row.description || '暂无' }}
          </template>
        </el-table-column>
        
        <el-table-column
          prop="proposer"
          label="创建人"
          min-width="100"
        >
          <template #default="scope">
            {{ scope.row.proposer || '暂无' }}
          </template>
        </el-table-column>
        
        <el-table-column
          prop="priority"
          label="优先级"
          width="100"
          align="center"
        >
          <template #default="scope">
            <el-tag
              :type="scope.row.priority === 3 ? 'danger' : scope.row.priority === 2 ? 'warning' : 'info'"
            >
              {{ scope.row.priority_display || (scope.row.priority === 3 ? '高' : scope.row.priority === 2 ? '中' : '低') }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column
          prop="status"
          label="状态"
          width="100"
          align="center"
        >
          <template #default="scope">
            <el-tag
              :type="scope.row.status === 'pending' ? 'info' : scope.row.status === 'ongoing' ? 'warning' : scope.row.status === 'completed' ? 'success' : 'danger'"
            >
              {{ scope.row.status_display || (scope.row.status === 'pending' ? '待处理' : scope.row.status === 'ongoing' ? '采集中' : scope.row.status === 'completed' ? '已完成' : '已取消') }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column
          prop="deadline"
          label="截止时间"
          min-width="160"
        >
          <template #default="scope">
            {{ scope.row.deadline || '暂无' }}
          </template>
        </el-table-column>
        
        <el-table-column
          label="操作"
          width="150"
          fixed="right"
        >
          <template #default="scope">
            <el-button
              type="primary"
              link
              @click="openEditDialog(scope.row)"
            >
              编辑
            </el-button>
            <el-button
              type="danger"
              link
              @click="handleDelete(scope.row.id)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          background
        />
      </div>
    </div>
    
    <!-- 需求表单对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="500px"
      destroy-on-close
    >
      <el-form
        ref="requirementFormRef"
        :model="requirementForm"
        :rules="rules"
        label-width="80px"
        label-position="right"
      >
        <el-form-item label="需求名称" prop="title">
          <el-input v-model="requirementForm.title" placeholder="请输入需求名称" />
        </el-form-item>
        
        <el-form-item label="需求描述" prop="description">
          <el-input 
            v-model="requirementForm.description" 
            type="textarea" 
            :rows="3" 
            placeholder="请输入需求描述" 
          />
        </el-form-item>
        
        <el-form-item label="创建人" prop="proposer">
          <el-input 
            v-model="requirementForm.proposer" 
            placeholder="创建人" 
            disabled
          />
        </el-form-item>
        
        <el-form-item label="优先级">
          <el-select v-model="requirementForm.priority" placeholder="请选择优先级">
            <el-option
              v-for="item in priorityOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="状态">
          <el-select v-model="requirementForm.status" placeholder="请选择状态">
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="截止时间">
          <el-date-picker
            v-model="requirementForm.deadline"
            type="datetime"
            placeholder="请选择截止时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            :clearable="true"
            style="width: 100%"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.requirement-manage-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.breadcrumb {
  font-size: 14px;
  color: #606266;
  
  .location {
    color: #909399;
  }
  
  .separator {
    margin: 0 8px;
    color: #C0C4CC;
  }
  
  .current {
    color: #303133;
    font-weight: 500;
  }
}

.page-title {
  margin-bottom: 20px;
  
  h1 {
    font-size: 24px;
    color: #303133;
    margin: 0;
    font-weight: 600;
  }
}

.page-content {
  background-color: #fff;
  border-radius: 4px;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.operation-bar {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  
  .search-input {
    width: 300px;
  }
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style> 