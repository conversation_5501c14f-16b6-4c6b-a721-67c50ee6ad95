import { ref, reactive } from 'vue'
import ROSLIB from 'roslib'
import { createCommandApi, getCommandsApi } from '@/common/request/api/commandInfo'
import { createTaskApi } from '@/common/request/api/tasksInfo'

function useHomeView() {
    // 采集人信息
    const collectorName = ref<string>('')
    // 采集需求选项
    const requirementOptions = ref([
        { value: 'indoor_mapping', label: '室内' },
        { value: 'outdoor_mapping', label: '室外' },
        { value: 'custom', label: '自定义需求' }
    ])
    // 采集指令选项
    const instructionOptions = ref([
        { value: 'test_mode', label: '测试模式' },
        { value: 'fast_mode', label: '快速模式' },
        { value: 'custom', label: '自定义指令' }
    ])
    // 客户端选择 - 根据提供的数据写死
    const clientOptions = ref([
        { value: '106.13.244.18:7001', label: 'p01-frp (106.13.244.18:7001)' },
        { value: '106.13.244.18:7002', label: 'p03-frp (106.13.244.18:7002)' },
        { value: '106.13.244.18:7003', label: 'p02-frp (106.13.244.18:7003)' },
        { value: '106.13.244.18:7006', label: 'p06-frp (106.13.244.18:7006)' },
        { value: '106.13.244.18:7007', label: 'p07-frp (106.13.244.18:7007)' },
        { value: '106.13.244.18:7009', label: 'p09-frp (106.13.244.18:7009)' },
        { value: '106.13.244.18:7010', label: 'p10-frp (106.13.244.18:7010)' }
    ])
    // 采集信息
    const collectionInfo = reactive({
        requirement: '' as string | number,
        instruction: '' as string | number,
        customInstruction: '' as string | number
    })

    // ROS 相关状态
    const ros = ref<ROSLIB.Ros | null>(null)
    const isRosConnected = ref(false)
    const rosError = ref<string | null>(null)
    const go2StateListener = ref<any>(null)
    const imageListener = ref<any>(null)
    const temperatureListener = ref<any>(null)
    const status_resp1 = ref<any>(null)

    // 状态管理
    const collectStatus = ref('停止')
    const verificationStatus = ref('暂无')
    const verificationMmessage = ref<string[]>([])
    const taskId = ref('')
    const showTaskId = ref(false)
    const recordingStatus = ref(false)
    const logs = ref<any[]>([])
    const autoScroll = ref(true)
    const selectedClientId = ref<string>('')
    const activeClientId = ref<string>('')
    const activeClientMachineId = ref<string>('')

    const startLoading = ref(false)  // 添加开始采集的loading状态
    const stopLoading = ref(false)   // 添加停止采集的loading状态

    // 采集计时相关变量
    const collectStartTime = ref<number | null>(null) // 采集开始时间戳（毫秒）
    const collectEndTime = ref<number | null>(null)   // 采集结束时间戳（毫秒）
    const collectDuration = ref<number>(0)            // 采集持续时长（秒）

    // 在状态管理部分添加客户端传感器状态
    // 客户端状态类型
    interface SensorStatus {
      hasIMU: boolean;
      hasColor: boolean;
    }

    type SensorStatusMap = Record<string, SensorStatus>;

    // 传感器状态管理
    const sensorStatus = ref<SensorStatusMap>({})

    // 添加一个帮助函数获取JSON对象摘要
    function getJsonSummary(obj: any): string {
        if (!obj) return '空对象';
        
        // 处理IMU类型消息
        if (obj.header && obj.header.frame_id) {
        // 只返回值类型，不返回frame_id本身
        if (obj.linear_acceleration) {
            return "IMU数据";
        } else if (obj.data) {
            return "图像数据";
        }
        return "ROS消息";
        }
        
        // 其他对象类型
        const type = Array.isArray(obj) ? 'Array' : 'Object';
        const size = Array.isArray(obj) ? `[${obj.length}]` : `{${Object.keys(obj).length}}`;
        return `${type}${size}`;
    }

    // 处理自定义指令：
    // 1. 创建指令(如果指令已存在，则不创建，但需要去搜索拿到指令id)
    // 2. 结合“自定义”需求 和 “自定义”指令 创建任务
    // 3. 返回任务ID
    const customTaskId = ref<string>('')
    async function handleCustomInstruction(requirement_id: number|string) {
        console.log('handleCustomInstruction', collectionInfo.customInstruction)
        if(collectionInfo.requirement === 39) { // 自定义需求
            const storedCollectorName = localStorage.getItem('collectorName')
            // 创建指令时只传递必要字段
            const createData = {
                description: collectionInfo.instruction.toString(),
                created_by_username: storedCollectorName
            }
            // 创建指令
            createCommandApi(createData).then(async (res: any) => {
                console.log('createCommand', res)
                // // 创建任务
                // 创建任务时传递数组格式的ID
                const createData = {
                    requirement_id: [requirement_id],
                    command_id: res.data.id,
                }
                const createTask = await createTaskApi(createData)
                console.log('createTask12321321', createTask.data[0].id)
                customTaskId.value = createTask.data[0].id
            }).catch((err: any) => {
                console.error('createCommand错误详情:', {
                    message: err.message,
                    name: err.name,
                    stack: err.stack,
                    fullError: err
                })
                if(err && err.response && err.response.data && err.response.data.message === '采集指令已存在') {
                    // 指令已存在，则搜索指令id
                    console.log('指令已存在，开始搜索指令ID...', collectionInfo.instruction.toString())
                    getCommandsApi({ description: collectionInfo.instruction.toString() }).then(async (res: any) => {
                        console.log('搜索指令结果:', res)
                        if (res.status === 'success' && res.data && res.data.length > 0) {
                            // 找到匹配的指令，获取第一个匹配项的ID
                            const commandId = res.data[0].id
                            console.log('找到已存在的指令ID:', commandId)
                            
                            // 使用找到的指令ID创建任务
                            const createData = {
                                requirement_id: [requirement_id],
                                command_id: commandId,
                            }
                            
                            try {
                                const createTask = await createTaskApi(createData)
                                console.log('使用已存在指令创建任务成功:', createTask.data[0].id)
                                customTaskId.value = createTask.data[0].id
                            } catch (taskError: any) {
                                console.error('创建任务失败:', taskError)
                            }
                        } else {
                            console.warn('搜索指令时未找到匹配的结果')
                        }
                    }).catch((err: any) => {
                        console.error('searchCommand错误详情:', {
                            message: err.message,
                            name: err.name,
                            stack: err.stack,
                            fullError: err
                        })
                    })
                }
                // 检查是否有响应数据
                if (err.response) {
                    console.error('服务器响应错误:', err.response)
                } else if (err.request) {
                    console.error('请求发送失败:', err.request)
                } else {
                    console.error('其他错误:', err.message)
                }
            })
        } 
    }

    function getTaskId() {
        return customTaskId.value
    }

    return {
        collectorName,
        requirementOptions,
        instructionOptions,
        clientOptions,
        collectionInfo,
        customTaskId,
        getTaskId,
        getJsonSummary,
        verificationMmessage,
        handleCustomInstruction,
        // ROS相关状态
        ros,
        isRosConnected,
        rosError,
        go2StateListener,
        imageListener,
        temperatureListener,
        status_resp1,
        
        // 状态管理
        collectStatus,
        verificationStatus,
        taskId,
        showTaskId,
        recordingStatus,
        logs,
        autoScroll,
        selectedClientId,
        activeClientId,
        activeClientMachineId,
        startLoading,
        stopLoading,
        // 传感器状态
        sensorStatus,
        // 采集计时相关变量
        collectStartTime,
        collectEndTime,
        collectDuration
    }
}

export default useHomeView

