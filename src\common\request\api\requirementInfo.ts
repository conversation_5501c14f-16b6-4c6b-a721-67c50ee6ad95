import { request } from '@/common/request/request'

const baseUrl = import.meta.env.VITE_API_BASE_URL

/**
 * 创建需求
 * @param requirementData 需求数据
 */
function createRequirementApi (requirementData: any): Promise<any> {
  return request({
    method: 'POST',
    url: baseUrl+'/collector/requirements/',
    data: requirementData
  })
}

/**
 * 删除需求
 * @param requirementId 需求ID
 */
function deleteRequirementApi (requirementId: string): Promise<any> {
  return request({
    method: 'DELETE',
    url: baseUrl + `/collector/requirements/${requirementId}/`,
    data: {}
  })
}

/**
 * 获取需求列表
 */
function getRequirementsApi (): Promise<any> {
  return request({
    method: 'GET',
    url: baseUrl + '/collector/requirements/',
    headers: {
      'Accept': 'application/json'
    }
  })
}

/**
 * 更新需求
 * @param requirementId 需求ID
 * @param requirementData 需求数据
 */
function updateRequirementApi (requirementId: string, requirementData: any): Promise<any> {
  return request({
    method: 'PUT',
    url: baseUrl + `/collector/requirements/${requirementId}/`,
    data: requirementData
  })
}

export {
  createRequirementApi,
  deleteRequirementApi,
  getRequirementsApi,
  updateRequirementApi
} 