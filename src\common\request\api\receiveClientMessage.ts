import { request } from '@/common/request/request'

/**
 * 接收客户端消息
 * @param clientId 客户端ID
 * @param message 消息内容
 * @param taskId 任务ID
 * @param type 消息类型
 * @param metadata 元数据（可选）
 */
function receiveClientMessageApi (params: {
  client_id: string, 
  message: string, 
  task_id: string | null, 
  type: string,
  filename?: string,
  filesize?: number
}): Promise<any> {
  const baseUrl = process.env.VUE_BASE_URL || ''
  return request({
    method: 'POST',
    url: `${baseUrl}/api/receive_client_message`,
    data: params
  })
}

export {
  receiveClientMessageApi,
} 