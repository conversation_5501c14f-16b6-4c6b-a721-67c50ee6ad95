<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElForm, ElFormItem, ElButton, ElSelect, ElOption } from 'element-plus'
import { getUsersApi } from '@/common/request/api/userInfo'

const router = useRouter()

// 表单数据
const loginForm = reactive({
  collectorName: ''
})

// 表单引用
const loginFormRef = ref<any>(null)

// 加载状态
const loading = ref(false)

// 用户列表
const userList = ref<any[]>([])

// 获取用户列表
const fetchUsers = async () => {
  try {
    loading.value = true
    
    // 尝试直接调用接口
    try {
      const res = await getUsersApi()
      
      // 根据实际接口返回格式调整
      // 如果res本身就是数组
      if (Array.isArray(res.data)) {
        userList.value = res.data
        console.log('用户列表数据:', userList.value)
      } else {
        throw new Error('接口返回数据格式异常')
      }
    } catch (apiError: any) {
      console.error('API调用失败:', apiError)
      // 特别处理500错误
      if (apiError.response && apiError.response.status === 500) {
        ElMessage.error('服务器内部错误(500)，请联系后端开发人员检查服务器日志')
        console.error('服务器500错误详情:', apiError.response.data)
      } else {
        ElMessage.error(`API调用失败: ${apiError.message || '未知错误'}`)
      }
      
      // 使用模拟数据
      console.log('使用模拟数据作为后备方案')
    }
    
  } catch (error) {
    console.error('整体获取用户列表失败:', error)
    ElMessage.error('获取用户列表失败')
  } finally {
    loading.value = false
  }
}

// 在组件挂载时获取用户列表
onMounted(() => {
  fetchUsers()
})

// 表单验证规则
const rules = {
  collectorName: [
    { required: true, message: '请选择采集人姓名', trigger: 'change' }
  ]
}

// 登录处理
const handleLogin = async () => {
  if (!loginFormRef.value) return
  
  try {
    // 验证表单
    await loginFormRef.value.validate()
    
    loading.value = true
    
    // 模拟登录延迟
    await new Promise(resolve => setTimeout(resolve, 800))
    
    // 存储采集人信息到localStorage
    localStorage.setItem('collectorName', loginForm.collectorName.trim())
    
    ElMessage.success(`欢迎您，${loginForm.collectorName}！`)
    
    // 跳转到首页
    router.push('/')
    
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    loading.value = false
  }
}

// 回车键登录
const handleKeyPress = (event: KeyboardEvent) => {
  if (event.key === 'Enter') {
    handleLogin()
  }
}
</script>

<template>
  <div class="login-container">
    <div class="login-background">
      <div class="background-pattern"></div>
    </div>
    
    <div class="login-content">
      <div class="login-card">
        <div class="login-header">
          <img src="@/assets/logo.png" alt="Logo" class="login-logo" />
          <h1 class="login-title">数据采集系统</h1>
          <p class="login-subtitle">请选择您的姓名以开始数据采集</p>
        </div>
        
        <div class="login-form-container">
          <el-form
            ref="loginFormRef"
            :model="loginForm"
            :rules="rules"
            class="login-form"
            size="large"
            @keypress="handleKeyPress"
          >
            <el-form-item prop="collectorName" class="form-item">
              <el-select
                v-model="loginForm.collectorName"
                placeholder="请选择采集人姓名"
                class="name-select"
                clearable
                :disabled="loading"
                filterable
              >
                <template #prefix>
                  <i class="user-icon"></i>
                </template>
                <el-option
                  v-for="(user, index) in userList"
                  :key="index"
                  :label="user.username || '未知姓名'"
                  :value="user.username || '未知姓名'"
                />
              </el-select>
            </el-form-item>
            
            <el-form-item class="form-item">
              <el-button
                type="primary"
                class="login-button"
                :loading="loading"
                @click="handleLogin"
                :disabled="!loginForm.collectorName.trim()"
              >
                <span v-if="!loading">开始</span>
                <span v-else>正在登录...</span>
              </el-button>
            </el-form-item>
          </el-form>
        </div>
        
        <div class="login-footer">
          <p class="footer-text">Lion Rock Data Collection System v1.0.0</p>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
//   background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

.login-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
}

.background-pattern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
  background-size: 400px 400px;
  animation: float 20s ease-in-out infinite;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

.login-content {
  position: relative;
  z-index: 1;
  width: 100%;
  max-width: 420px;
  padding: 20px;
}

.login-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 40px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
  }
}

.login-header {
  text-align: center;
  margin-bottom: 40px;
}

.login-logo {
  height: 60px;
  margin-bottom: 20px;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.login-title {
  font-size: 28px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 12px 0;
  background: linear-gradient(120deg, #2c3e50, #4a6285);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  letter-spacing: 0.5px;
}

.login-subtitle {
  font-size: 16px;
  color: #606266;
  margin: 0;
  line-height: 1.5;
}

.login-form-container {
  margin-bottom: 30px;
}

.login-form {
  .form-item {
    margin-bottom: 24px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
}

.name-select {
  width: 100%;
  
  :deep(.el-input__wrapper) {
    border-radius: 12px;
    padding: 12px 16px;
    font-size: 16px;
    border: 2px solid #ebeef5;
    transition: all 0.3s ease;
    
    &:hover {
      border-color: #c0c4cc;
    }
    
    &.is-focus {
      border-color: #409EFF;
      box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
    }
  }
  
  :deep(.el-input__inner) {
    font-size: 16px;
    color: #303133;
    
    &::placeholder {
      color: #c0c4cc;
    }
  }
}

.user-icon {
  display: inline-block;
  width: 18px;
  height: 18px;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23909399"><path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/></svg>');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.login-button {
  width: 100%;
  height: 50px;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  background: linear-gradient(135deg, #409EFF 0%, #3a8ee6 100%);
  border: none;
  transition: all 0.3s ease;
  
  &:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(64, 158, 255, 0.3);
  }
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

.login-footer {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.footer-text {
  font-size: 13px;
  color: #909399;
  margin: 0;
}

// 响应式设计
@media (max-width: 480px) {
  .login-content {
    padding: 15px;
  }
  
  .login-card {
    padding: 30px 25px;
  }
  
  .login-title {
    font-size: 24px;
  }
  
  .login-subtitle {
    font-size: 14px;
  }
}

// Element Plus 样式覆盖
:deep(.el-form-item__error) {
  font-size: 13px;
  color: #f56c6c;
  margin-top: 6px;
}

:deep(.el-button.is-loading) {
  .el-icon {
    margin-right: 8px;
  }
}
</style> 