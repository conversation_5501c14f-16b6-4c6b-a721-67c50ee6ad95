<script setup lang="ts">
import { onMounted } from 'vue'
import useMachineManage from './machineManage'

const { 
  // 响应式数据
  machineList, 
  pagination, 
  loading,
  searchKeyword, 
  machineForm, 
  machineFormRef, 
  dialogVisible, 
  dialogTitle, 
  rules,
  
  // 数据操作函数
  
  // 表单处理函数
  
  // 对话框操作函数
  openCreateDialog,
  openEditDialog,
  submitForm,
  
  // 删除和分页函数
  handleDelete,
  handleCurrentChange,
  handleSizeChange,
  handleSearch,
  
  // 初始化函数
  initMachineManage
} = useMachineManage()

// 页面加载时初始化
onMounted(() => {
  initMachineManage()
})
</script>

<template>
  <div class="machine-manage-container">
    <div class="page-header">
      <div class="breadcrumb">
        <span>您当前的位置：</span>
        <span class="location">数据采集系统</span>
        <span class="separator">/</span>
        <span class="current">设备管理</span>
      </div>
    </div>
    
    <div class="page-title">
      <h1>设备管理</h1>
    </div>
    
    <div class="page-content">
      <!-- 操作栏 -->
      <div class="operation-bar">
        <div class="left-actions">
          <el-button type="primary" @click="openCreateDialog">
            新建设备
          </el-button>
        </div>
        
        <div class="right-actions">
          <el-input
            v-model="searchKeyword"
            placeholder="请输入设备名称或IP地址"
            clearable
            @keyup.enter="handleSearch"
            class="search-input"
          >
            <template #append>
              <el-button @click="handleSearch">
                搜索
              </el-button>
            </template>
          </el-input>
        </div>
      </div>
      
      <!-- 设备表格 -->
      <el-table
        :data="machineList"
        style="width: 100%"
        border
        :loading="loading"
        max-height="500"
      >
        <el-table-column
          type="index"
          label="序号"
          width="80"
          align="center"
        />
        
        <el-table-column
          prop="id"
          label="设备ID"
          min-width="100"
        >
          <template #default="scope">
            {{ scope.row.id || '暂无' }}
          </template>
        </el-table-column>
        
        <el-table-column
          prop="name"
          label="设备名称"
          min-width="150"
        >
          <template #default="scope">
            {{ scope.row.name || '暂无' }}
          </template>
        </el-table-column>
        
        <el-table-column
          prop="ip_address"
          label="IP地址"
          min-width="140"
        >
          <template #default="scope">
            {{ scope.row.ip_address || '暂无' }}
          </template>
        </el-table-column>
        
        <el-table-column
          prop="port"
          label="端口"
          min-width="80"
        >
          <template #default="scope">
            {{ scope.row.port || '暂无' }}
          </template>
        </el-table-column>
        
        <el-table-column
          prop="description"
          label="设备描述"
          min-width="120"
        >
          <template #default="scope">
            {{ scope.row.description || '暂无' }}
          </template>
        </el-table-column>
        
        <el-table-column
          prop="created_by_username"
          label="创建人"
          min-width="100"
        >
          <template #default="scope">
            {{ scope.row.created_by_username || '暂无' }}
          </template>
        </el-table-column>
        
        <el-table-column
          prop="created_at"
          label="创建时间"
          min-width="160"
        >
          <template #default="scope">
            {{ scope.row.created_at || '暂无' }}
          </template>
        </el-table-column>
        
        <el-table-column
          prop="updated_at"
          label="更新时间"
          min-width="160"
        >
          <template #default="scope">
            {{ scope.row.updated_at || '暂无' }}
          </template>
        </el-table-column>
        
        <el-table-column
          label="操作"
          width="150"
          fixed="right"
        >
          <template #default="scope">
            <el-button
              type="primary"
              link
              @click="openEditDialog(scope.row)"
            >
              编辑
            </el-button>
            <el-button
              type="danger"
              link
              @click="handleDelete(scope.row.id)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          background
        />
      </div>
    </div>
    
    <!-- 设备表单对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="600px"
      destroy-on-close
    >
      <el-form
        ref="machineFormRef"
        :model="machineForm"
        :rules="rules"
        label-width="100px"
        label-position="right"
      >
        <el-form-item label="设备名称" prop="name">
          <el-input v-model="machineForm.name" placeholder="请输入设备名称" />
        </el-form-item>
        
        <el-form-item label="IP地址" prop="ip_address">
          <el-input v-model="machineForm.ip_address" placeholder="请输入IP地址" />
        </el-form-item>
        
        <el-form-item label="端口" prop="port">
          <el-input v-model="machineForm.port" placeholder="请输入端口号" />
        </el-form-item>
        
        <el-form-item label="设备描述" prop="description">
          <el-input 
            v-model="machineForm.description" 
            type="textarea" 
            :rows="3" 
            placeholder="请输入设备描述" 
          />
        </el-form-item>
        
        <el-form-item label="创建人" prop="created_by_username">
          <el-input 
            v-model="machineForm.created_by_username" 
            placeholder="创建人" 
            disabled
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.machine-manage-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.breadcrumb {
  font-size: 14px;
  color: #606266;
  
  .location {
    color: #909399;
  }
  
  .separator {
    margin: 0 8px;
    color: #C0C4CC;
  }
  
  .current {
    color: #303133;
    font-weight: 500;
  }
}

.page-title {
  margin-bottom: 20px;
  
  h1 {
    font-size: 24px;
    color: #303133;
    margin: 0;
    font-weight: 600;
  }
}

.page-content {
  background-color: #fff;
  border-radius: 4px;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.operation-bar {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  
  .search-input {
    width: 300px;
  }
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style> 