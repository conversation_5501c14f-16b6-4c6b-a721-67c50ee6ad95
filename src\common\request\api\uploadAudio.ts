import { request } from '@/common/request/request'

/**
 * 上传音频文件
 * @param formData 包含音频文件的FormData对象
 */
function uploadAudioApi (formData: FormData): Promise<any> {
  const baseUrl = process.env.VUE_BASE_URL || ''
  return request({
    method: 'POST',
    url: `${baseUrl}/api/upload_audio`,
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

export {
  uploadAudioApi,
} 