import { request } from '@/common/request/request'

const baseUrl = import.meta.env.VITE_API_BASE_URL

/**
 * 获取指令列表
 * @param params 查询参数 { description?: string, current?: number, size?: number }
 */
function getCommandsApi (params?: { description?: string, current?: number, size?: number }): Promise<any> {
  const queryParams = new URLSearchParams()
  
  // 添加搜索参数
  if (params?.description) {
    queryParams.append('description', params.description)
  }
  
  // 添加分页参数
  if (params?.current) {
    queryParams.append('current', params.current.toString())
  }
  
  if (params?.size) {
    queryParams.append('size', params.size.toString())
  }
  
  const url = baseUrl + '/collector/commands/' + (queryParams.toString() ? '?' + queryParams.toString() : '')
  
  return request({
    method: 'GET',
    url: url,
    headers: {
      'Accept': 'application/json'
    }
  })
}

/**
 * 创建指令
 * @param commandData 指令数据
 */
function createCommandApi (commandData: any): Promise<any> {
  return request({
    method: 'POST',
    url: baseUrl + '/collector/commands/',
    data: commandData
  })
}

/**
 * 更新指令
 * @param commandId 指令ID
 * @param commandData 指令数据
 */
function updateCommandApi (commandId: string, commandData: any): Promise<any> {
  return request({
    method: 'PUT',
    url: baseUrl + `/collector/commands/${commandId}/`,
    data: commandData
  })
}

/**
 * 删除指令
 * @param commandId 指令ID
 */
function deleteCommandApi (commandId: string): Promise<any> {
  return request({
    method: 'DELETE',
    url: baseUrl + `/collector/commands/${commandId}/`,
    data: {}
  })
}

/**
 * 通过CSV文件批量导入采集指令
 * @param formData FormData对象
 */
function uploadCommandFileApi (formData: FormData): Promise<any> {
  return request({
    method: 'POST',
    url: baseUrl + `/collector/commands/import-csv/`,
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

export {
  getCommandsApi,
  createCommandApi,
  updateCommandApi,
  deleteCommandApi,
  uploadCommandFileApi
} 