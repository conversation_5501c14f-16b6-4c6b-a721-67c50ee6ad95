<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { createCommandApi, getCommandsApi, updateCommandApi, deleteCommandApi, uploadCommandFileApi } from '@/common/request/api/commandInfo'

// 指令列表
const commandList = ref<any[]>([])

// 分页信息
const pagination = ref({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 加载状态
const loading = ref(false)

// 搜索关键词
const searchKeyword = ref('')
const storedCollectorName = localStorage.getItem('collectorName')
const storedCollectorId = localStorage.getItem('collectorId')

// 指令表单
const commandForm = ref({
  id: '',
  description: '',
  created_by_id: storedCollectorId || '',
  created_by_username: storedCollectorName || ''
})

// 表单对话框状态
const dialogVisible = ref(false)
const dialogTitle = ref('新建指令')
const dialogType = ref('create') // 'create' 或 'edit'

// 上传对话框状态
const uploadDialogVisible = ref(false)
const uploadLoading = ref(false)
const uploadFileList = ref<any[]>([])

// 表单引用
const commandFormRef = ref<any>(null)
const uploadRef = ref<any>(null)

// 表单验证规则
const rules = {
  description: [
    { required: true, message: '请输入指令描述', trigger: 'blur' }
  ]
}

// 重置表单
const resetForm = () => {
  commandForm.value = {
    id: '',
    description: '',
    created_by_id: storedCollectorId || '',
    created_by_username: storedCollectorName || ''
  }
}

// 获取指令列表
const fetchCommands = async () => {
  try {
    loading.value = true
    
    try {
      console.log('正在请求指令列表...')
      
      // 构建查询参数
      const params: any = {
        current: pagination.value.currentPage,
        size: pagination.value.pageSize
      }
      
      // 如果有搜索关键词，添加到参数中
      if (searchKeyword.value && searchKeyword.value.trim()) {
        params.description = searchKeyword.value.trim()
      }
      
      const res = await getCommandsApi(params)
      console.log('指令列表数据:', res)
      
      if (res.status === 'success' && Array.isArray(res.data)) {
        // 确保每个指令数据都有id字段
        commandList.value = res.data.map((command: any) => {
          // 如果没有id字段，可以暂时使用临时id
          if (!command.id) {
            console.warn(`指令缺少id字段，使用临时id`)
            return { ...command, id: `temp_${Math.random().toString(36).substring(2, 9)}` }
          }
          return command
        })
        
        // 更新分页信息
        if (res.total_count !== undefined) {
          pagination.value.total = res.total_count
        } else {
          pagination.value.total = res.data.length
        }
        
        console.log('分页信息:', pagination.value)
      } else {
        throw new Error('接口返回数据格式异常')
      }
      
    } catch (apiError: any) {
      console.error('API调用失败:', apiError)
      
      // 特别处理500错误
      if (apiError.response && apiError.response.status === 500) {
        console.error('服务器500错误详情:', apiError.response.data)
      } else {
      }
    }
  } catch (error: any) {
    console.error('获取指令列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 打开新建指令对话框
const openCreateDialog = () => {
  resetForm()
  // 设置创建人为当前登录用户
  // 不设置created_by_id，仅设置用户名
  commandForm.value.created_by_username = storedCollectorName || ''
  dialogType.value = 'create'
  dialogTitle.value = '新建指令'
  dialogVisible.value = true
  
  nextTick(() => {
    if (commandFormRef.value) {
      commandFormRef.value.clearValidate()
    }
  })
}

// 打开编辑指令对话框
const openEditDialog = (row: any) => {
  resetForm()
  dialogType.value = 'edit'
  dialogTitle.value = '编辑指令'
  
  // 填充表单数据
  if (row.id) commandForm.value.id = row.id
  if (row.description) commandForm.value.description = row.description
  if (row.created_by_id) commandForm.value.created_by_id = row.created_by_id
  if (row.created_by_username) commandForm.value.created_by_username = row.created_by_username
  
  // 如果没有id字段，给出警告
  if (!row.id) {
    console.warn('编辑的指令数据缺少id字段，可能导致更新操作失败')
    ElMessage({ message: '指令数据缺少ID，编辑功能可能受限', type: 'warning', duration: 1200 })
  }
  
  dialogVisible.value = true
  
  nextTick(() => {
    if (commandFormRef.value) {
      commandFormRef.value.clearValidate()
    }
  })
}

// 提交指令表单
const submitForm = async () => {
  if (!commandFormRef.value) return
  
  try {
    await commandFormRef.value.validate()
    
    if (dialogType.value === 'create') {
      // 创建指令时只传递必要字段
      const createData = {
        description: commandForm.value.description,
        created_by_username: commandForm.value.created_by_username
      }
      // 创建指令
      await createCommandApi(createData)
      ElMessage({ message: '指令创建成功', type: 'success', duration: 1200 })
    } else {
      // 更新指令
      await updateCommandApi(commandForm.value.id, commandForm.value)
      ElMessage({ message: '指令更新成功', type: 'success', duration: 1200 })
    }
    
    dialogVisible.value = false
    fetchCommands() // 刷新列表
  } catch (error: any) {
    console.error('表单提交失败:', error)
    ElMessage({ message: `操作失败: ${error.message || '表单验证失败，请检查输入'}`, type: 'error', duration: 1200 })
  }
}

// 删除指令
const handleDelete = async (id: string | undefined) => {
  if (!id) {
    ElMessage({ message: '指令数据缺少ID，无法删除', type: 'warning', duration: 1200 })
    return
  }
  
  try {
    await ElMessageBox.confirm('确定要删除该指令吗？此操作不可逆，同时会删除该指令对应的所有任务', '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消', 
      type: 'warning'
    })
    
    try {
      // 调用删除API
      const res = await deleteCommandApi(id)
      console.log('删除响应:', res)
      
      // 删除成功，刷新列表
      ElMessage({ message: '指令删除成功', type: 'success', duration: 1200 })
      fetchCommands()
    } catch (apiError: any) {
      console.error('API错误:', apiError)
      
      // 尝试解析错误信息
      try {
        if (typeof apiError.message === 'string' && apiError.message.includes('bizError')) {
          const errorObj = JSON.parse(apiError.message)
          // 如果错误消息中包含"删除成功"，则视为成功
          if (errorObj.message && errorObj.message.includes('删除成功')) {
            ElMessage({ message: errorObj.message, type: 'success', duration: 1200 })
            fetchCommands() // 刷新列表
            return
          }
        }
      } catch (parseError) {
        console.error('解析错误信息失败:', parseError)
      }
      
      // 其他错误情况
      ElMessage({ message: `删除指令失败: ${apiError.message || '未知错误'}`, type: 'error', duration: 1200 })
    }
  } catch (error: any) {
    // 用户取消操作，不做处理
    if (error === 'cancel') {
      return
    }
    console.error('操作错误:', error)
  }
}

// 处理分页变化
const handleCurrentChange = (page: number) => {
  pagination.value.currentPage = page
  fetchCommands()
}

// 处理每页显示条数变化
const handleSizeChange = (size: number) => {
  pagination.value.pageSize = size
  pagination.value.currentPage = 1
  fetchCommands()
}

// 处理搜索
const handleSearch = () => {
  pagination.value.currentPage = 1
  fetchCommands()
}

// 清除搜索
const clearSearch = () => {
  searchKeyword.value = ''
  pagination.value.currentPage = 1
  fetchCommands()
}

// 打开上传对话框
const openUploadDialog = () => {
  uploadFileList.value = []
  uploadDialogVisible.value = true
}

// 处理文件上传前的校验
const beforeUpload = (file: any) => {
  const isCSV = file.type === 'text/csv' || file.name.toLowerCase().endsWith('.csv')
  if (!isCSV) {
    ElMessage.error('只支持上传CSV格式的文件！')
    return false
  }
  
  const isLt10M = file.size / 1024 / 1024 < 10
  if (!isLt10M) {
    ElMessage.error('上传文件大小不能超过 10MB!')
    return false
  }
  
  return true
}

// 处理文件选择
const handleFileChange = (file: any, fileList: any[]) => {
  console.log(file)
  uploadFileList.value = fileList
}

// 处理文件移除
const handleRemove = (file: any, fileList: any[]) => {
  console.log(file)
  uploadFileList.value = fileList
}

// 执行上传
const handleUpload = async () => {
  if (uploadFileList.value.length === 0) {
    ElMessage.warning('请先选择要上传的CSV文件！')
    return
  }
  
  try {
    uploadLoading.value = true
    
    const formData = new FormData()
    // 后端期望的字段名是 csv_file
    formData.append('csv_file', uploadFileList.value[0].raw)
    
    // 添加创建人信息
    if (storedCollectorName) {
      formData.append('created_by_username', storedCollectorName)
    }
    
    const result = await uploadCommandFileApi(formData)
    
    // 处理上传结果，显示详细信息
    if (result && result.summary) {
      const { created_count, skipped_count, error_count } = result.summary
      let message = `上传完成！成功创建 ${created_count} 条指令`
      
      if (skipped_count > 0) {
        message += `，跳过 ${skipped_count} 条重复指令`
      }
      
      if (error_count > 0) {
        message += `，${error_count} 条数据存在错误`
        ElMessage.warning(message)
      } else {
        ElMessage.success(message)
      }
    } else {
      ElMessage.success('指令文件上传成功！')
    }
    
    uploadDialogVisible.value = false
    uploadFileList.value = []
    fetchCommands() // 刷新列表
    
  } catch (error: any) {
    console.error('上传失败:', error)
    ElMessage.error(`上传失败: ${error.message || '未知错误'}`)
  } finally {
    uploadLoading.value = false
  }
}

// 取消上传
const cancelUpload = () => {
  uploadFileList.value = []
  uploadDialogVisible.value = false
}

// 页面加载时获取指令列表
onMounted(() => {
  fetchCommands()
})
</script>

<template>
  <div class="command-view-container">
    <div class="page-header">
      <div class="breadcrumb">
        <span>您当前的位置：</span>
        <span class="location">数据采集系统</span>
        <span class="separator">/</span>
        <span class="current">指令管理</span>
      </div>
    </div>
    
    <div class="page-title">
      <h1>指令管理</h1>
    </div>
    
    <div class="page-content">
      <!-- 操作栏 -->
      <div class="operation-bar">
        <div class="left-actions">
          <el-button type="primary" @click="openCreateDialog">
            新建指令
          </el-button>
          <el-button type="success" @click="openUploadDialog">
            上传指令
          </el-button>
        </div>
        
        <div class="right-actions">
          <el-input
            v-model="searchKeyword"
            placeholder="请输入指令描述"
            clearable
            @keyup.enter="handleSearch"
            @clear="clearSearch"
            class="search-input"
          >
            <template #append>
              <el-button @click="handleSearch">
                搜索
              </el-button>
            </template>
          </el-input>
        </div>
      </div>
      
      <!-- 搜索状态提示 -->
      <div v-if="searchKeyword && searchKeyword.trim()" class="search-status">
        <el-alert
          :title="`正在搜索: ${searchKeyword}`"
          type="info"
          :closable="false"
          show-icon
        >
          <template #default>
            <span>共找到 {{ pagination.total }} 条相关指令</span>
            <el-button type="text" @click="clearSearch" style="margin-left: 10px;">
              清除搜索
            </el-button>
          </template>
        </el-alert>
      </div>
      
      <!-- 指令表格 -->
      <el-table
        :data="commandList"
        style="width: 100%"
        border
        :loading="loading"
        max-height="500"
      >
        <el-table-column
          type="index"
          label="序号"
          width="80"
          align="center"
        />
        
        <el-table-column
          prop="id"
          label="指令ID"
          min-width="100"
        >
          <template #default="scope">
            {{ scope.row.id || '暂无' }}
          </template>
        </el-table-column>
        
        <el-table-column
          prop="description"
          label="指令描述"
          min-width="200"
          show-overflow-tooltip
        >
          <template #default="scope">
            {{ scope.row.description || '暂无' }}
          </template>
        </el-table-column>
        
        <el-table-column
          prop="created_by_username"
          label="创建人"
          min-width="100"
        >
          <template #default="scope">
            {{ scope.row.created_by_username || '暂无' }}
          </template>
        </el-table-column>
        
        <el-table-column
          prop="created_at"
          label="创建时间"
          min-width="160"
        >
          <template #default="scope">
            {{ scope.row.created_at || '暂无' }}
          </template>
        </el-table-column>
        
        <el-table-column
          prop="updated_at"
          label="更新时间"
          min-width="160"
        >
          <template #default="scope">
            {{ scope.row.updated_at || '暂无' }}
          </template>
        </el-table-column>
        
        <el-table-column
          label="操作"
          width="150"
          fixed="right"
        >
          <template #default="scope">
            <el-button
              type="primary"
              link
              @click="openEditDialog(scope.row)"
            >
              编辑
            </el-button>
            <el-button
              type="danger"
              link
              @click="handleDelete(scope.row.id)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          background
        />
      </div>
    </div>
    
    <!-- 指令表单对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="500px"
      destroy-on-close
    >
      <el-form
        ref="commandFormRef"
        :model="commandForm"
        :rules="rules"
        label-width="80px"
        label-position="right"
      >
        <el-form-item label="指令描述" prop="description">
          <el-input 
            v-model="commandForm.description" 
            type="textarea" 
            :rows="3" 
            placeholder="请输入指令描述" 
          />
        </el-form-item>
        
        <el-form-item label="创建人" prop="created_by_username">
          <el-input 
            v-model="commandForm.created_by_username" 
            placeholder="创建人" 
            disabled
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">确定</el-button>
        </div>
      </template>
    </el-dialog>
    
    <!-- 上传指令对话框 -->
    <el-dialog
      v-model="uploadDialogVisible"
      title="批量上传指令"
      width="500px"
      destroy-on-close
    >
      <div class="upload-content">
        <div class="upload-tips">
          <el-alert
            title="上传说明"
            type="info"
            :closable="false"
            show-icon
          >
            <template #default>
              <div>
                <p>• 仅支持上传CSV格式的文件</p>
                <p>• 文件大小不能超过10MB</p>
                <p>• CSV文件第一列应为指令描述</p>
                <p>• 创建人：{{ storedCollectorName || '未登录' }}</p>
              </div>
            </template>
          </el-alert>
        </div>
        
        <div class="upload-area">
          <el-upload
            ref="uploadRef"
            :file-list="uploadFileList"
            :before-upload="beforeUpload"
            :on-change="handleFileChange"
            :on-remove="handleRemove"
            :auto-upload="false"
            accept=".csv"
            drag
            multiple
            :limit="1"
          >
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">
              将CSV文件拖到此处，或<em>点击上传</em>
            </div>
            <template #tip>
              <div class="el-upload__tip">
                只能上传CSV文件，且不超过10MB
              </div>
            </template>
          </el-upload>
        </div>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelUpload">取消</el-button>
          <el-button 
            type="primary" 
            @click="handleUpload"
            :loading="uploadLoading"
            :disabled="uploadFileList.length === 0"
          >
            确认上传
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.command-view-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.breadcrumb {
  font-size: 14px;
  color: #606266;
  
  .location {
    color: #909399;
  }
  
  .separator {
    margin: 0 8px;
    color: #C0C4CC;
  }
  
  .current {
    color: #303133;
    font-weight: 500;
  }
}

.page-title {
  margin-bottom: 20px;
  
  h1 {
    font-size: 24px;
    color: #303133;
    margin: 0;
    font-weight: 600;
  }
}

.page-content {
  background-color: #fff;
  border-radius: 4px;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.operation-bar {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  
  .search-input {
    width: 300px;
  }
}

.search-status {
  margin-bottom: 16px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.upload-content {
  .upload-tips {
    margin-bottom: 20px;
    
    :deep(.el-alert__content) {
      p {
        margin: 4px 0;
        font-size: 14px;
        line-height: 1.6;
      }
    }
  }
  
  .upload-area {
    :deep(.el-upload-dragger) {
      width: 100%;
      height: 180px;
      border: 2px dashed #d9d9d9;
      border-radius: 6px;
      background-color: #fafafa;
      text-align: center;
      cursor: pointer;
      position: relative;
      overflow: hidden;
      transition: border-color 0.2s;
      
      &:hover {
        border-color: #409eff;
      }
      
      .el-icon-upload {
        font-size: 67px;
        color: #c0c4cc;
        margin: 40px 0 16px;
        line-height: 50px;
      }
    }
    
    :deep(.el-upload__text) {
      color: #606266;
      font-size: 14px;
      text-align: center;
      
      em {
        color: #409eff;
        font-style: normal;
      }
    }
    
    :deep(.el-upload__tip) {
      font-size: 12px;
      color: #909399;
      margin-top: 7px;
    }
  }
}
</style> 