import { ref, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { createMachineApi, getMachinesApi, updateMachineApi, deleteMachineApi } from '@/common/request/api/machineInfo'

function useMachineManage() {
    // 设备列表
    const machineList = ref<any[]>([])

    // 分页信息
    const pagination = ref({
        currentPage: 1,
        pageSize: 10,
        total: 0
    })

    // 加载状态
    const loading = ref(false)

    // 搜索关键词
    const searchKeyword = ref('')
    const storedCollectorName = localStorage.getItem('collectorName')

    // 设备表单
    const machineForm = ref({
        id: '',
        name: '',
        ip_address: '',
        port: '',
        description: '',
        created_by_username: storedCollectorName || ''
    })

    // 表单对话框状态
    const dialogVisible = ref(false)
    const dialogTitle = ref('新建设备')
    const dialogType = ref('create') // 'create' 或 'edit'

    // 表单引用
    const machineFormRef = ref<any>(null)

    // 表单验证规则
    const rules = {
        name: [
            { required: true, message: '请输入设备名称', trigger: 'blur' },
            { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        ip_address: [
            { pattern: /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/, message: '请输入正确的IP地址格式', trigger: 'blur' }
        ]
    }

    // 重置表单
    const resetForm = () => {
        machineForm.value = {
            id: '',
            name: '',
            ip_address: '',
            port: '',
            description: '',
            created_by_username: storedCollectorName || ''
        }
    }

    // 获取设备列表
    const fetchMachines = async () => {
        try {
            loading.value = true
            
            try {
                const res = await getMachinesApi()
                console.log('设备列表数据:', res)
                
                if (Array.isArray(res.data)) {
                    // 确保每个设备数据都有id字段
                    machineList.value = res.data.map((machine: any) => {
                        // 如果没有id字段，可以暂时使用name作为临时id
                        if (!machine.id && machine.name) {
                            console.warn(`设备 ${machine.name} 缺少id字段，使用临时id`)
                            return { ...machine, id: `temp_${machine.name}` }
                        }
                        return machine
                    })
                    pagination.value.total = res.data.length
                } else {
                    throw new Error('接口返回数据格式异常')
                }
                
                // 分页处理
                const startIndex = (pagination.value.currentPage - 1) * pagination.value.pageSize
                const endIndex = startIndex + pagination.value.pageSize
                machineList.value = machineList.value.slice(startIndex, endIndex)
                
            } catch (apiError: any) {
                console.error('API调用失败:', apiError)
                
                // 特别处理500错误
                if (apiError.response && apiError.response.status === 500) {
                    ElMessage.error('服务器内部错误(500)，请联系后端开发人员检查服务器日志')
                    console.error('服务器500错误详情:', apiError.response.data)
                } else {
                    ElMessage.error(`API调用失败: ${apiError.message || '未知错误'}`)
                }
            }
        } catch (error: any) {
            console.error('获取设备列表失败:', error)
            ElMessage.error(`获取设备列表失败: ${error.message || '未知错误'}`)
        } finally {
            loading.value = false
        }
    }

    // 打开新建设备对话框
    const openCreateDialog = () => {
        resetForm()
        // 设置创建人为当前登录用户
        machineForm.value.created_by_username = storedCollectorName || ''
        dialogType.value = 'create'
        dialogTitle.value = '新建设备'
        dialogVisible.value = true
        
        nextTick(() => {
            if (machineFormRef.value) {
                machineFormRef.value.clearValidate()
            }
        })
    }

    // 打开编辑设备对话框
    const openEditDialog = (row: any) => {
        resetForm()
        dialogType.value = 'edit'
        dialogTitle.value = '编辑设备'
        
        // 填充表单数据
        if (row.id) machineForm.value.id = row.id
        if (row.name) machineForm.value.name = row.name
        if (row.ip_address) machineForm.value.ip_address = row.ip_address
        if (row.port) machineForm.value.port = row.port
        if (row.description) machineForm.value.description = row.description
        if (row.created_by_username) machineForm.value.created_by_username = row.created_by_username
        
        // 如果没有id字段，给出警告
        if (!row.id) {
            console.warn('编辑的设备数据缺少id字段，可能导致更新操作失败')
            ElMessage.warning('设备数据缺少ID，编辑功能可能受限')
        }
        
        dialogVisible.value = true
        
        nextTick(() => {
            if (machineFormRef.value) {
                machineFormRef.value.clearValidate()
            }
        })
    }

    // 提交设备表单
    const submitForm = async () => {
        if (!machineFormRef.value) return
        
        try {
            await machineFormRef.value.validate()
            
            if (dialogType.value === 'create') {
                // 创建设备时传递必要字段
                const createData = {
                    name: machineForm.value.name,
                    ip_address: machineForm.value.ip_address,
                    port: machineForm.value.port,
                    description: machineForm.value.description,
                    created_by_username: machineForm.value.created_by_username
                }
                // 创建设备
                await createMachineApi(createData)
                ElMessage.success('设备创建成功')
            } else {
                // 更新设备时传递必要字段
                const updateData = {
                    id: machineForm.value.id,
                    name: machineForm.value.name,
                    ip_address: machineForm.value.ip_address,
                    port: machineForm.value.port,
                    description: machineForm.value.description,
                    created_by_username: machineForm.value.created_by_username
                }
                // 更新设备
                await updateMachineApi(machineForm.value.id, updateData)
                ElMessage.success('设备更新成功')
            }
            
            dialogVisible.value = false
            fetchMachines() // 刷新列表
        } catch (error: any) {
            console.error('表单提交失败:', error)
            ElMessage.error(`操作失败: ${error.message || '表单验证失败，请检查输入'}`)
        }
    }

    // 删除设备
    const handleDelete = async (id: string | undefined) => {
        if (!id) {
            ElMessage.warning('设备数据缺少ID，无法删除')
            return
        }
        
        try {
            await ElMessageBox.confirm('确定要删除该设备吗？此操作不可逆', '警告', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
            
            try {
                // 调用删除API
                const res = await deleteMachineApi(id)
                console.log('删除响应:', res)
                
                // 删除成功，刷新列表
                ElMessage.success('设备删除成功')
                fetchMachines()
            } catch (apiError: any) {
                console.error('API错误:', apiError)
                
                // 尝试解析错误信息
                try {
                    if (typeof apiError.message === 'string' && apiError.message.includes('bizError')) {
                        const errorObj = JSON.parse(apiError.message)
                        // 如果错误消息中包含"删除成功"，则视为成功
                        if (errorObj.message && errorObj.message.includes('删除成功')) {
                            ElMessage.success(errorObj.message)
                            fetchMachines() // 刷新列表
                            return
                        }
                    }
                } catch (parseError) {
                    console.error('解析错误信息失败:', parseError)
                }
                
                // 其他错误情况
                ElMessage.error(`删除设备失败: ${apiError.message || '未知错误'}`)
            }
        } catch (error: any) {
            // 用户取消操作，不做处理
            if (error === 'cancel') {
                return
            }
            console.error('操作错误:', error)
        }
    }

    // 处理分页变化
    const handleCurrentChange = (page: number) => {
        pagination.value.currentPage = page
        fetchMachines()
    }

    // 处理每页显示条数变化
    const handleSizeChange = (size: number) => {
        pagination.value.pageSize = size
        pagination.value.currentPage = 1
        fetchMachines()
    }

    // 处理搜索
    const handleSearch = () => {
        pagination.value.currentPage = 1
        fetchMachines()
    }

    // 初始化函数
    const initMachineManage = () => {
        fetchMachines()
    }

    return {
        // 响应式数据
        machineList,
        pagination,
        loading,
        searchKeyword,
        storedCollectorName,
        machineForm,
        machineFormRef,
        dialogVisible,
        dialogTitle,
        dialogType,
        rules,
        
        // 数据操作函数
        fetchMachines,
        
        // 表单处理函数
        resetForm,
        
        // 对话框操作函数
        openCreateDialog,
        openEditDialog,
        submitForm,
        
        // 删除和分页函数
        handleDelete,
        handleCurrentChange,
        handleSizeChange,
        handleSearch,
        
        // 初始化函数
        initMachineManage
    }
}

export default useMachineManage