import { ref, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { createTaskApi, getTasksApi, updateTaskApi, deleteTaskApi } from '@/common/request/api/tasksInfo'
import { getRequirementsApi } from '@/common/request/api/requirementInfo'
import { getCommandsApi, uploadCommandFileApi } from '@/common/request/api/commandInfo'

function useTaskManage() {
    const taskList = ref<any[]>([])
    const requirementList = ref<any[]>([])
    const commandList = ref<any[]>([])
    
    // 分页信息
    const pagination = ref({
        currentPage: 1,
        pageSize: 10,
        total: 0
    })
    
    // 加载状态
    const loading = ref(false)
    const requirementsLoading = ref(false)
    const commandsLoading = ref(false)
    
    // 表单对话框状态
    const dialogVisible = ref(false)
    const dialogTitle = ref('新建任务')
    const dialogType = ref('create') // 'create' 或 'edit'

    // 文件上传相关状态
    const uploadFileList = ref<any[]>([])
    const uploadLoading = ref(false)

    // 表单引用
    const taskFormRef = ref<any>(null)
    const uploadRef = ref<any>(null)

    // 表单验证规则
    const rules = {
        requirement_titles: [
            { required: true, message: '请选择至少一个需求名称', trigger: 'change' }
        ],
        command_descriptions: [
            { required: true, message: '请选择至少一个指令描述', trigger: 'change' }
        ],
        target: [
            { required: true, message: '请输入目标数量', trigger: 'blur' },
            { type: 'number', message: '目标数量必须为数字', trigger: 'blur' }
        ]
    }

    // 搜索关键词
    const searchKeyword = ref('')
    const storedCollectorName = localStorage.getItem('collectorName')

    // 任务表单
    const taskForm = ref({
        id: '',
        requirement_ids: [] as number[],
        requirement_titles: [] as string[],
        command_ids: [] as number[],
        command_descriptions: [] as string[],
        deadline: '',
        target: 0
    })

    // 重置表单
    const resetForm = () => {
        taskForm.value = {
            id: '',
            requirement_ids: [],
            requirement_titles: [],
            command_ids: [],
            command_descriptions: [],
            deadline: '',
            target: 0
        }
        uploadFileList.value = []
    }

    // 获取需求列表
    const fetchRequirements = async () => {
        try {
            requirementsLoading.value = true
            const res = await getRequirementsApi()
            
            if (Array.isArray(res.data)) {
                requirementList.value = res.data
                console.log('获取到的需求列表:', requirementList.value)
            } else {
                throw new Error('需求接口返回数据格式异常')
            }
        } catch (error: any) {
            console.error('获取需求列表失败:', error)
            ElMessage.error(`获取需求列表失败: ${error.message || '未知错误'}`)
        } finally {
            requirementsLoading.value = false
        }
    }

    // 获取指令列表
    const fetchCommands = async () => {
        try {
            commandsLoading.value = true
            const res = await getCommandsApi()
            
            if (Array.isArray(res.data)) {
                commandList.value = res.data
            } else {
                throw new Error('指令接口返回数据格式异常')
            }
        } catch (error: any) {
            console.error('获取指令列表失败:', error)
            ElMessage.error(`获取指令列表失败: ${error.message || '未知错误'}`)
        } finally {
            commandsLoading.value = false
        }
    }

    // 获取任务列表
    const fetchTasks = async () => {
        try {
            loading.value = true
            
            try {
                console.log('正在请求任务列表...')
                const res = await getTasksApi()
                console.log('任务列表数据:', res)
                
                if (Array.isArray(res.data)) {
                    // 确保每个任务数据都有id字段
                    taskList.value = res.data.map((task: any) => {
                        // 如果没有id字段，可以暂时使用name作为临时id
                        if (!task.id && task.requirement_title) {
                            console.warn(`任务 ${task.requirement_title} 缺少id字段，使用临时id`)
                            return { ...task, id: `temp_${task.requirement_title}` }
                        }
                        return task
                    })
                    pagination.value.total = res.data.length
                    console.log('获取到的任务列表:', taskList.value)
                } else {
                    throw new Error('接口返回数据格式异常')
                }
                
                // 分页处理
                const startIndex = (pagination.value.currentPage - 1) * pagination.value.pageSize
                const endIndex = startIndex + pagination.value.pageSize
                taskList.value = taskList.value.slice(startIndex, endIndex)
                
            } catch (apiError: any) {
                console.error('API调用失败:', apiError)
                
                // 特别处理500错误
                if (apiError.response && apiError.response.status === 500) {
                    ElMessage.error('服务器内部错误(500)，请联系后端开发人员检查服务器日志')
                    console.error('服务器500错误详情:', apiError.response.data)
                } else {
                    ElMessage.error(`API调用失败: ${apiError.message || '未知错误'}`)
                }
            }
        } catch (error: any) {
            console.error('获取任务列表失败:', error)
            ElMessage.error(`获取任务列表失败: ${error.message || '未知错误'}`)
        } finally {
            loading.value = false
        }
    }

    // 处理需求选择 - 多选
    const handleRequirementChange = (values: string[]) => {
        // 根据选择的需求名称数组，找到对应的需求ID数组
        taskForm.value.requirement_titles = values
        taskForm.value.requirement_ids = values.map(title => {
            const requirement = requirementList.value.find(item => item.title === title)
            return requirement ? requirement.id : null
        }).filter(id => id !== null)
    }

    // 处理指令选择 - 多选（编辑时使用）
    const handleCommandChange = (values: string[]) => {
        // 根据选择的指令描述数组，找到对应的指令ID数组
        taskForm.value.command_descriptions = values
        taskForm.value.command_ids = values.map(description => {
            const command = commandList.value.find(item => item.description === description)
            return command ? command.id : null
        }).filter(id => id !== null)
    }

    // 处理文件上传前的校验
    const beforeUpload = (file: any) => {
        const isCSV = file.type === 'text/csv' || file.name.toLowerCase().endsWith('.csv')
        if (!isCSV) {
            ElMessage.error('只支持上传CSV格式的文件！')
            return false
        }
        
        const isLt10M = file.size / 1024 / 1024 < 10
        if (!isLt10M) {
            ElMessage.error('上传文件大小不能超过 10MB!')
            return false
        }
        
        return true
    }

    // 处理文件选择
    const handleFileChange = (file: any, fileList: any[]) => {
        console.log('文件变化:', file)
        uploadFileList.value = fileList
    }

    // 处理文件移除
    const handleRemove = (file: any, fileList: any[]) => {
        console.log('移除文件:', file)
        uploadFileList.value = fileList
    }

    // 批量上传指令并创建任务
    const uploadCommandsAndCreateTasks = async () => {
        if (uploadFileList.value.length === 0) {
            ElMessage.warning('请先选择要上传的CSV文件！')
            return false
        }

        try {
            uploadLoading.value = true
            
            // 第一步：上传CSV文件，批量创建指令
            const formData = new FormData()
            formData.append('csv_file', uploadFileList.value[0].raw)
            
            if (storedCollectorName) {
                formData.append('created_by_username', storedCollectorName)
            }
            
            console.log('开始上传指令文件...')
            const uploadResult = await uploadCommandFileApi(formData)
            console.log('指令上传结果:', uploadResult)
            
            if (!uploadResult || !uploadResult.summary || uploadResult.summary.created_count === 0) {
                throw new Error('未成功创建任何指令，无法继续创建任务')
            }
            
            // 显示上传结果
            const { created_count, skipped_count, error_count } = uploadResult.summary
            let uploadMessage = `成功上传 ${created_count} 条指令`
            if (skipped_count > 0) {
                uploadMessage += `，跳过 ${skipped_count} 条重复指令`
            }
            if (error_count > 0) {
                uploadMessage += `，${error_count} 条数据存在错误`
            }
            
            console.log(uploadMessage)
            
            // 第二步：获取最新的指令列表
            console.log('开始获取最新的指令列表...')
            const commandsResponse = await getCommandsApi({
                current: 1,
                size: 1000 // 获取足够多的数据
            })
            
            if (!commandsResponse || !commandsResponse.data || !Array.isArray(commandsResponse.data)) {
                throw new Error('获取指令列表失败')
            }
            
            // 获取最新创建的指令（假设按创建时间倒序，取前N条）
            const recentCommands = commandsResponse.data.slice(0, created_count)
            console.log('获取到的最新指令:', recentCommands)
            
            if (recentCommands.length === 0) {
                throw new Error('未找到新创建的指令')
            }
            
            // 第三步：为每个需求和每个指令的组合创建任务
            let createdTaskCount = 0
            let failedTaskCount = 0
            
            console.log('开始批量创建任务...')
            for (const requirementId of taskForm.value.requirement_ids) {
                for (const command of recentCommands) {
                    try {
                        const createData = {
                            requirement_id: [requirementId],
                            command_id: [command.id],
                            deadline: taskForm.value.deadline,
                            target: taskForm.value.target
                        }
                        
                        console.log('创建任务数据:', createData)
                        await createTaskApi(createData)
                        createdTaskCount++
                    } catch (taskError: any) {
                        console.error('创建任务失败:', taskError)
                        failedTaskCount++
                    }
                }
            }
            
            // 显示最终结果
            let finalMessage = `任务创建完成！成功创建 ${createdTaskCount} 个任务`
            if (failedTaskCount > 0) {
                finalMessage += `，${failedTaskCount} 个任务创建失败`
                ElMessage.warning(finalMessage)
            } else {
                ElMessage.success(finalMessage)
            }
            
            return true
            
        } catch (error: any) {
            console.error('批量创建失败:', error)
            ElMessage.error(`操作失败: ${error.message || '未知错误'}`)
            return false
        } finally {
            uploadLoading.value = false
        }
    }

    // 打开新建任务对话框
    const openCreateDialog = () => {
        resetForm()
        dialogType.value = 'create'
        dialogTitle.value = '新建任务'
        dialogVisible.value = true
        
        // 确保需求和指令列表已加载
        if (requirementList.value.length === 0) {
            fetchRequirements()
        }
        if (commandList.value.length === 0) {
            fetchCommands()
        }
        
        nextTick(() => {
            if (taskFormRef.value) {
                taskFormRef.value.clearValidate()
            }
        })
    }

    // 打开编辑任务对话框
    const openEditDialog = (row: any) => {
        resetForm()
        dialogType.value = 'edit'
        dialogTitle.value = '编辑任务'
        
        // 确保需求和指令列表已加载
        if (requirementList.value.length === 0) {
            fetchRequirements()
        }
        if (commandList.value.length === 0) {
            fetchCommands()
        }
        
        // 填充表单数据 - 支持单个和多个值
        if (row.id) taskForm.value.id = row.id
        
        // 处理需求数据 - 支持单个和多个
        if (row.requirement_ids && Array.isArray(row.requirement_ids)) {
            taskForm.value.requirement_ids = row.requirement_ids
        } else if (row.requirement_id) {
            taskForm.value.requirement_ids = [row.requirement_id]
        }
        if (row.requirement_titles && Array.isArray(row.requirement_titles)) {
            taskForm.value.requirement_titles = row.requirement_titles
        } else if (row.requirement_title) {
            taskForm.value.requirement_titles = [row.requirement_title]
        }
        
        // 处理指令数据 - 支持单个和多个
        if (row.command_ids && Array.isArray(row.command_ids)) {
            taskForm.value.command_ids = row.command_ids
        } else if (row.command_id) {
            taskForm.value.command_ids = [row.command_id]
        }
        if (row.command_descriptions && Array.isArray(row.command_descriptions)) {
            taskForm.value.command_descriptions = row.command_descriptions
        } else if (row.command_description) {
            taskForm.value.command_descriptions = [row.command_description]
        }
        
        if (row.deadline) taskForm.value.deadline = row.deadline
        if (row.target) taskForm.value.target = row.target
        
        // 如果没有id字段，给出警告
        if (!row.id) {
            console.warn('编辑的任务数据缺少id字段，可能导致更新操作失败')
            ElMessage.warning('任务数据缺少ID，编辑功能可能受限')
        }
        
        dialogVisible.value = true
        
        nextTick(() => {
            if (taskFormRef.value) {
                taskFormRef.value.clearValidate()
            }
        })
    }

    // 提交任务表单
    const submitForm = async () => {
        if (!taskFormRef.value) return
        
        try {
            if (dialogType.value === 'create') {
                // 新建任务：自定义验证逻辑
                if (!taskForm.value.requirement_titles || taskForm.value.requirement_titles.length === 0) {
                    ElMessage.error('请选择至少一个需求名称')
                    return
                }
                if (!taskForm.value.target || taskForm.value.target <= 0) {
                    ElMessage.error('请输入有效的目标数量')
                    return
                }
                if (uploadFileList.value.length === 0) {
                    ElMessage.error('请选择要上传的CSV指令文件')
                    return
                }
                
                // 创建任务：先上传CSV生成指令，再批量创建任务
                const success = await uploadCommandsAndCreateTasks()
                if (success) {
                    dialogVisible.value = false
                    fetchTasks() // 刷新列表
                }
            } else {
                // 编辑任务：使用完整的表单验证
                await taskFormRef.value.validate()
                
                // 更新任务时传递数组格式的ID
                const updateData = {
                    id: taskForm.value.id,
                    requirement_ids: taskForm.value.requirement_ids,
                    requirement_titles: taskForm.value.requirement_titles,
                    command_ids: taskForm.value.command_ids,
                    command_descriptions: taskForm.value.command_descriptions,
                    deadline: taskForm.value.deadline,
                    target: taskForm.value.target
                }
                // 更新任务
                await updateTaskApi(taskForm.value.id, updateData)
                ElMessage.success('任务更新成功')
                dialogVisible.value = false
                fetchTasks() // 刷新列表
            }
        } catch (error: any) {
            console.error('表单提交失败:', error)
            ElMessage.error(`操作失败: ${error.message || '表单验证失败，请检查输入'}`)
        }
    }

    // 删除任务
    const handleDelete = async (id: string | undefined) => {
        if (!id) {
            ElMessage.warning('任务数据缺少ID，无法删除')
            return
        }
        
        try {
            await ElMessageBox.confirm('确定要删除该任务吗？此操作不可逆', '警告', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
            
            try {
                // 调用删除API
                const res = await deleteTaskApi(id)
                console.log('删除响应:', res)
                
                // 删除成功，刷新列表
                ElMessage.success('任务删除成功')
                fetchTasks()
            } catch (apiError: any) {
                console.error('API错误:', apiError)
                
                // 尝试解析错误信息
                try {
                    if (typeof apiError.message === 'string' && apiError.message.includes('bizError')) {
                        const errorObj = JSON.parse(apiError.message)
                        // 如果错误消息中包含"删除成功"，则视为成功
                        if (errorObj.message && errorObj.message.includes('删除成功')) {
                            ElMessage.success(errorObj.message)
                            fetchTasks() // 刷新列表
                            return
                        }
                    }
                } catch (parseError) {
                    console.error('解析错误信息失败:', parseError)
                }
                
                // 其他错误情况
                ElMessage.error(`删除任务失败: ${apiError.message || '未知错误'}`)
            }
        } catch (error: any) {
            // 用户取消操作，不做处理
            if (error === 'cancel') {
                return
            }
            console.error('操作错误:', error)
        }
    }

    // 处理分页变化
    const handleCurrentChange = (page: number) => {
        pagination.value.currentPage = page
        fetchTasks()
    }

    // 处理每页显示条数变化
    const handleSizeChange = (size: number) => {
        pagination.value.pageSize = size
        pagination.value.currentPage = 1
        fetchTasks()
    }

    // 处理搜索
    const handleSearch = () => {
        pagination.value.currentPage = 1
        fetchTasks()
    }

    // 初始化函数
    const initTaskManage = () => {
        fetchTasks()
        fetchRequirements()
        fetchCommands()
    }

    return {
        // 响应式数据
        taskList,
        requirementList,
        commandList,
        pagination,
        loading,
        requirementsLoading,
        commandsLoading,
        dialogVisible,
        dialogTitle,
        dialogType,
        uploadFileList,
        uploadLoading,
        taskFormRef,
        uploadRef,
        rules,
        searchKeyword,
        storedCollectorName,
        taskForm,
        
        // 数据操作函数
        fetchRequirements,
        fetchCommands,
        fetchTasks,
        
        // 表单处理函数
        resetForm,
        handleRequirementChange,
        handleCommandChange,
        
        // 文件处理函数
        beforeUpload,
        handleFileChange,
        handleRemove,
        uploadCommandsAndCreateTasks,
        
        // 对话框操作函数
        openCreateDialog,
        openEditDialog,
        submitForm,
        
        // 删除和分页函数
        handleDelete,
        handleCurrentChange,
        handleSizeChange,
        handleSearch,
        
        // 初始化函数
        initTaskManage
    }
}

export default useTaskManage