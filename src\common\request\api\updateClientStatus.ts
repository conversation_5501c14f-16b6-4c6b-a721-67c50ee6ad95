import { request } from '@/common/request/request'

/**
 * 更新客户端状态
 * @param clientId 客户端ID
 * @param status 状态 
 * @param taskId 任务ID
 */
function updateClientStatusApi (clientId: string, status: string, taskId: string | null): Promise<any> {
  const baseUrl = process.env.VUE_BASE_URL || ''
  return request({
    method: 'POST',
    url: `${baseUrl}/api/update_client_status`,
    data: {
      client_id: clientId,
      status: status,
      task_id: taskId
    }
  })
}

export {
  updateClientStatusApi,
} 