<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue'
import { ElMessage, ElSelect, ElOption, ElTag } from 'element-plus'
import { createDetailApi, getDetailsApi, updateDetailApi } from '@/common/request/api/detailsInfo'
import { getRequirementsApi } from '@/common/request/api/requirementInfo'
import { getCommandsApi } from '@/common/request/api/commandInfo'
import { getTasksApi } from '@/common/request/api/tasksInfo'
import { getMachinesApi } from '@/common/request/api/machineInfo'

// 定义 props
interface Props {
  isInHomePage?: boolean  // 是否在首页中使用
  hideHeader?: boolean    // 是否隐藏页面头部
}

const props = withDefaults(defineProps<Props>(), {
  isInHomePage: false,
  hideHeader: false
})

// 采集结果列表
const detailList = ref<any[]>([])

// 需求列表、指令列表和任务列表
const requirementList = ref<any[]>([])
const commandList = ref<any[]>([])
const taskList = ref<any[]>([])
const filteredTaskList = ref<any[]>([])

// 分页信息
const pagination = ref({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 加载状态
const loading = ref(false)
const requirementsLoading = ref(false)
const commandsLoading = ref(false)
const tasksLoading = ref(false)
const machinesLoading = ref(false)

const storedCollectorName = localStorage.getItem('collectorName')

// 搜索表单
const searchForm = ref({
  requirement_title: '',
  command_description: '',
  executed_by_username: props.isInHomePage ? (storedCollectorName || '') : ''
})

// 采集结果表单
const detailForm = ref({
  id: '',
  requirement_id: '',
  requirement_title: '',
  command_id: '',
  command_description: '',
  task_id: '',
  executed_by_username: storedCollectorName || '',
  execution_time: '',
  status: 'success',
  time_consumption: 0,
  machine_id: '',
  machine_name: ''
})

// 表单对话框状态
const dialogVisible = ref(false)
const dialogTitle = ref('新建采集结果')
const dialogType = ref('create') // 'create' 或 'edit'

// 表单引用
const detailFormRef = ref<any>(null)

// 状态选项
const statusOptions = [
  { label: '成功', value: 'success' },
  { label: '失败', value: 'failed' },
  { label: '取消', value: 'cancel' }
]

// 表单验证规则
const rules = {
  requirement_title: [
    { required: true, message: '请选择需求', trigger: 'change' }
  ],
  command_description: [
    { required: true, message: '请选择指令', trigger: 'change' }
  ],
  task_id: [
    { required: true, message: '请选择任务', trigger: 'change' }
  ]
}

// 重置表单
// const resetForm = () => {
//   detailForm.value = {
//     id: '',
//     requirement_id: '',
//     requirement_title: '',
//     command_id: '',
//     command_description: '',
//     task_id: '',
//     executed_by_username: storedCollectorName || '',
//     execution_time: '',
//     status: 'success',
//     time_consumption: 0,
//     machine_id: '',
//     machine_name: ''
//   }
//   filteredTaskList.value = []
// }

// 获取需求列表
const fetchRequirements = async () => {
  try {
    requirementsLoading.value = true
    const res = await getRequirementsApi()
    
    if (Array.isArray(res.data)) {
      requirementList.value = res.data
      console.log('获取到的需求列表:', requirementList.value)
    } else {
      throw new Error('需求接口返回数据格式异常')
    }
  } catch (error: any) {
    console.error('获取需求列表失败:', error)
    ElMessage.error(`获取需求列表失败: ${error.message || '未知错误'}`)
  } finally {
    requirementsLoading.value = false
  }
}

// 获取指令列表
const fetchCommands = async () => {
  try {
    commandsLoading.value = true
    const res = await getCommandsApi()
    
    if (Array.isArray(res.data)) {
      commandList.value = res.data
    } else {
      throw new Error('指令接口返回数据格式异常')
    }
  } catch (error: any) {
    console.error('获取指令列表失败:', error)
    ElMessage.error(`获取指令列表失败: ${error.message || '未知错误'}`)
  } finally {
    commandsLoading.value = false
  }
}

// 获取任务列表
const fetchTasks = async () => {
  try {
    tasksLoading.value = true
    const res = await getTasksApi()
    
    if (Array.isArray(res.data)) {
      taskList.value = res.data
      console.log('获取到的任务列表:', taskList.value)
    } else {
      throw new Error('任务接口返回数据格式异常')
    }
  } catch (error: any) {
    console.error('获取任务列表失败:', error)
    ElMessage.error(`获取任务列表失败: ${error.message || '未知错误'}`)
  } finally {
    tasksLoading.value = false
  }
}

// 获取设备列表
const fetchMachines = async () => {
  try {
    machinesLoading.value = true
    const res = await getMachinesApi()
    if (Array.isArray(res.data)) {
      machineList.value = res.data
    } else {
      throw new Error('设备接口返回数据格式异常')
    }
  } catch (error: any) {
    ElMessage.error(`获取设备列表失败: ${error.message || '未知错误'}`)
  } finally {
    machinesLoading.value = false
  }
}

// 处理需求选择
const handleRequirementChange = (value: string) => {
  // 根据选择的需求名称，找到对应的需求ID
  const selectedRequirement = requirementList.value.find(item => item.title === value)
  if (selectedRequirement) {
    detailForm.value.requirement_id = selectedRequirement.id
  } else {
    detailForm.value.requirement_id = ''
  }
  
  // 清空指令和任务选择
  detailForm.value.command_description = ''
  detailForm.value.command_id = ''
  detailForm.value.task_id = ''
  filteredTaskList.value = []
  
  // 更新任务列表
  updateTaskList()
}

// 处理指令选择
const handleCommandChange = (value: string) => {
  // 根据选择的指令描述，找到对应的指令ID
  const selectedCommand = commandList.value.find(item => item.description === value)
  if (selectedCommand) {
    detailForm.value.command_id = selectedCommand.id
  } else {
    detailForm.value.command_id = ''
  }
  
  // 清空任务选择
  detailForm.value.task_id = ''
  
  // 更新任务列表
  updateTaskList()
}

// 根据需求和指令筛选任务列表
const updateTaskList = () => {
  if (detailForm.value.requirement_id && detailForm.value.command_id) {
    // 筛选出符合条件的任务
    filteredTaskList.value = taskList.value.filter(task => 
      task.requirement_id === detailForm.value.requirement_id && 
      task.command_id === detailForm.value.command_id
    )
    console.log('筛选后的任务列表:', filteredTaskList.value)
  } else {
    filteredTaskList.value = []
  }
}

// 设备名称下拉选择事件
const handleMachineChange = (machineName: string) => {
  const selected = machineList.value.find(item => item.name === machineName)
  if (selected) {
    detailForm.value.machine_id = selected.id
    detailForm.value.machine_name = selected.name
  } else {
    detailForm.value.machine_id = ''
    detailForm.value.machine_name = ''
  }
}

// 获取采集结果列表
const fetchDetails = async () => {
  try {
    loading.value = true
    
    // 构建请求参数 - 必须包含分页参数
    const params: any = {
      current: pagination.value.currentPage,
      size: pagination.value.pageSize
    }
    
    // 不再有简单搜索，所有搜索都通过搜索表单
    
    // 搜索条件
    console.log('=== 开始处理搜索条件 ===')
    console.log('搜索表单值:', JSON.stringify(searchForm.value, null, 2))
    
    // 需求名称搜索
    if (searchForm.value.requirement_title.trim()) {
      params.requirement_title = searchForm.value.requirement_title.trim()
      console.log('✓ 添加需求名称搜索:', params.requirement_title)
    }
    
    // 指令描述搜索
    if (searchForm.value.command_description.trim()) {
      params.command_description = searchForm.value.command_description.trim()
      console.log('✓ 添加指令描述搜索:', params.command_description)
    }
    
    // 采集人搜索
    if (searchForm.value.executed_by_username.trim()) {
      params.executed_by_username = searchForm.value.executed_by_username.trim()
      console.log('✓ 添加采集人搜索:', params.executed_by_username)
    }
    
    // 注意：后端不支持status和task_id参数，所以这里注释掉
    // if (searchForm.value.status) {
    //   params.status = searchForm.value.status
    //   console.log('✓ 添加状态搜索:', params.status)
    // }
    
    // if (searchForm.value.task_id && searchForm.value.task_id.trim()) {
    //   params.task_id = searchForm.value.task_id.trim()
    //   console.log('✓ 添加任务ID搜索:', params.task_id)
    // }
    
    // 所有搜索条件都已经是后端支持的
    
    console.log('最终构建的params:', params)
    
    console.log('正在请求采集结果列表，参数:', params)
    const res = await getDetailsApi(params)
    console.log('采集结果列表数据:', res)
    
    if (res.status === 'success') {
      // 处理新的分页响应格式
      if (Array.isArray(res.data)) {
        detailList.value = res.data.map((detail: any) => {
          if (!detail.id) {
            detail.id = `temp_${Math.random().toString(36).substring(2, 9)}`
          }
          return {
            ...detail,
            machine_id: detail.machine ? detail.machine.id : detail.machine_id || '',
            machine_name: detail.machine ? detail.machine.name : detail.machine_name || ''
          }
        })
        
        // 更新分页信息
        pagination.value.total = res.total_count || 0
        pagination.value.currentPage = res.current || 1
        pagination.value.pageSize = res.size || 10
        
        console.log('获取到的采集结果列表:', detailList.value)
        console.log('分页信息:', {
          total: pagination.value.total,
          current: pagination.value.currentPage,
          size: pagination.value.pageSize
        })
      } else {
        console.warn('获取到的数据格式异常:', res)
        detailList.value = []
        pagination.value.total = 0
      }
    } else if (res.data && Array.isArray(res.data.data)) {
      // 兼容旧的响应格式
      detailList.value = res.data.data.map((detail: any) => {
        if (!detail.id) {
          detail.id = `temp_${Math.random().toString(36).substring(2, 9)}`
        }
        return {
          ...detail,
          machine_id: detail.machine ? detail.machine.id : '',
          machine_name: detail.machine ? detail.machine.name : ''
        }
      })
      pagination.value.total = res.data.count || res.data.data.length
    } else if (Array.isArray(res.data)) {
      // 兼容没有分页的旧格式
      detailList.value = res.data.map((detail: any) => {
        if (!detail.id) {
          console.warn(`采集结果缺少id字段，使用临时id`)
          return { ...detail, id: `temp_${Math.random().toString(36).substring(2, 9)}` }
        }
        return detail
      })
      pagination.value.total = res.data.length
    } else {
      throw new Error('接口返回数据格式异常')
    }
      
  } catch (error: any) {
    console.error('获取采集结果列表失败:', error)
    
    // 特别处理500错误
    if (error.response && error.response.status === 500) {
      ElMessage.error('服务器内部错误(500)，请联系后端开发人员检查服务器日志')
      console.error('服务器500错误详情:', error.response.data)
    } else {
      ElMessage.error(`获取采集结果列表失败: ${error.message || '未知错误'}`)
    }
  } finally {
    loading.value = false
  }
}

// // 打开新建采集结果对话框
// const openCreateDialog = () => {
//   resetForm()
//   // 设置采集人为当前登录用户
//   detailForm.value.executed_by_username = storedCollectorName || ''
//   dialogType.value = 'create'
//   dialogTitle.value = '新建采集结果'
//   dialogVisible.value = true
  
//   // 确保数据已加载
//   if (requirementList.value.length === 0) {
//     fetchRequirements()
//   }
//   if (commandList.value.length === 0) {
//     fetchCommands()
//   }
//   if (taskList.value.length === 0) {
//     fetchTasks()
//   }
//   if (machineList.value.length === 0) {
//     fetchMachines()
//   }
  
//   nextTick(() => {
//     if (detailFormRef.value) {
//       detailFormRef.value.clearValidate()
//     }
//   })
// }

// // 打开编辑采集结果对话框
// const openEditDialog = (row: any) => {
//   resetForm()
//   dialogType.value = 'edit'
//   dialogTitle.value = '编辑采集结果'
  
//   // 填充表单数据
//   if (row.id) detailForm.value.id = row.id
//   if (row.requirement_id) detailForm.value.requirement_id = row.requirement_id
//   if (row.requirement_title) detailForm.value.requirement_title = row.requirement_title
//   if (row.command_id) detailForm.value.command_id = row.command_id
//   if (row.command_description) detailForm.value.command_description = row.command_description
//   if (row.task_id) detailForm.value.task_id = row.task_id
//   if (row.executed_by_username) detailForm.value.executed_by_username = row.executed_by_username
//   if (row.execution_time) detailForm.value.execution_time = row.execution_time
//   if (row.status) detailForm.value.status = row.status
//   if (row.time_consumption) detailForm.value.time_consumption = row.time_consumption
//   if (row.machine_id) detailForm.value.machine_id = row.machine_id
//   if (row.machine_name) detailForm.value.machine_name = row.machine_name
  
//   // 确保数据已加载
//   if (requirementList.value.length === 0) {
//     fetchRequirements()
//   }
//   if (commandList.value.length === 0) {
//     fetchCommands()
//   }
//   if (taskList.value.length === 0) {
//     fetchTasks()
//   }
//   if (machineList.value.length === 0) {
//     fetchMachines()
//   }
  
//   // 更新任务列表
//   setTimeout(() => {
//     updateTaskList()
//   }, 100)
  
//   // 如果没有id字段，给出警告
//   if (!row.id) {
//     console.warn('编辑的采集结果数据缺少id字段，可能导致更新操作失败')
//     ElMessage.warning('采集结果数据缺少ID，编辑功能可能受限')
//   }
  
//   dialogVisible.value = true
  
//   nextTick(() => {
//     if (detailFormRef.value) {
//       detailFormRef.value.clearValidate()
//     }
//   })
// }

// 提交采集结果表单
const submitForm = async () => {
  if (!detailFormRef.value) return
  
  try {
    await detailFormRef.value.validate()
    
    if (dialogType.value === 'create') {
      // 创建采集结果时传递必要字段
      const createData = {
        task_id: detailForm.value.task_id,
        executed_by_username: detailForm.value.executed_by_username,
        status: detailForm.value.status,
        time_consumption: detailForm.value.time_consumption,
        machine_id: detailForm.value.machine_id
        // machine_name: detailForm.value.machine_name
      }
      // 创建采集结果
      await createDetailApi(createData)
      ElMessage.success('采集结果创建成功')
    } else {
      // 更新采集结果时传递必要字段
      const updateData = {
        id: detailForm.value.id,
        task_id: detailForm.value.task_id,
        executed_by_username: detailForm.value.executed_by_username,
        status: detailForm.value.status,
        time_consumption: detailForm.value.time_consumption,
        machine_id: detailForm.value.machine_id,
        machine_name: detailForm.value.machine_name
      }
      // 更新采集结果
      await updateDetailApi(detailForm.value.id, updateData)
      ElMessage.success('采集结果更新成功')
    }
    
    dialogVisible.value = false
    fetchDetails() // 刷新列表
  } catch (error: any) {
    console.error('表单提交失败:', error)
    ElMessage.error(`操作失败: ${error.message || '表单验证失败，请检查输入'}`)
  }
}

// // 删除采集结果
// const handleDelete = async (id: string | undefined) => {
//   if (!id) {
//     ElMessage.warning('采集结果数据缺少ID，无法删除')
//     return
//   }
  
//   try {
//     await ElMessageBox.confirm('确定要删除该采集结果吗？此操作不可逆', '警告', {
//       confirmButtonText: '确定',
//       cancelButtonText: '取消',
//       type: 'warning'
//     })
    
//     try {
//       // 调用删除API
//       const res = await deleteDetailApi(id)
//       console.log('删除响应:', res)
      
//       // 删除成功，刷新列表
//       ElMessage.success('采集结果删除成功')
//       fetchDetails()
//     } catch (apiError: any) {
//       console.error('API错误:', apiError)
      
//       // 尝试解析错误信息
//       try {
//         if (typeof apiError.message === 'string' && apiError.message.includes('bizError')) {
//           const errorObj = JSON.parse(apiError.message)
//           // 如果错误消息中包含"删除成功"，则视为成功
//           if (errorObj.message && errorObj.message.includes('删除成功')) {
//             ElMessage.success(errorObj.message)
//             fetchDetails() // 刷新列表
//             return
//           }
//         }
//       } catch (parseError) {
//         console.error('解析错误信息失败:', parseError)
//       }
      
//       // 其他错误情况
//       ElMessage.error(`删除采集结果失败: ${apiError.message || '未知错误'}`)
//     }
//   } catch (error: any) {
//     // 用户取消操作，不做处理
//     if (error === 'cancel') {
//       return
//     }
//     console.error('操作错误:', error)
//   }
// }

// 处理分页变化
const handleCurrentChange = (page: number) => {
  pagination.value.currentPage = page
  fetchDetails()
}

// 处理每页显示条数变化
const handleSizeChange = (size: number) => {
  pagination.value.pageSize = size
  pagination.value.currentPage = 1
  fetchDetails()
}

// 搜索
const handleSearch = () => {
  console.log('=== 搜索按钮被点击 ===')
  console.log('当前搜索表单值:', JSON.stringify(searchForm.value, null, 2))
  pagination.value.currentPage = 1
  fetchDetails()
}

// 重置搜索
const handleReset = () => {
  searchForm.value = {
    requirement_title: '',
    command_description: '',
    executed_by_username: props.isInHomePage ? (storedCollectorName || '') : ''
  }
  pagination.value.currentPage = 1
  fetchDetails()
}

// 检查是否有搜索条件
const hasSearchConditions = computed(() => {
  return searchForm.value.requirement_title.trim() ||
         searchForm.value.command_description.trim() ||
         searchForm.value.executed_by_username.trim()
})

// 设备列表
const machineList = ref<any[]>([])

// 懒加载辅助数据（需求、指令、任务、设备）
// const ensureAuxiliaryDataLoaded = async () => {
//   if (props.isInHomePage) {
//     // 只在首页模式下才需要懒加载
//     const promises = []
    
//     if (requirementList.value.length === 0) {
//       promises.push(fetchRequirements())
//     }
//     if (commandList.value.length === 0) {
//       promises.push(fetchCommands())
//     }
//     if (taskList.value.length === 0) {
//       promises.push(fetchTasks())
//     }
//     if (machineList.value.length === 0) {
//       promises.push(fetchMachines())
//     }
    
//     if (promises.length > 0) {
//       await Promise.all(promises)
//     }
//   }
// }

// 页面加载时只获取采集结果列表，其他数据懒加载
onMounted(() => {
  fetchDetails()
  // 在首页模式下不需要立即加载其他数据，只在需要时才加载
  if (!props.isInHomePage) {
    // 只有在独立页面才需要立即加载所有数据
    fetchRequirements()
    fetchCommands()
    fetchTasks()
    fetchMachines()
  }
})

// 监听isInHomePage变化，重新获取数据
watch(() => props.isInHomePage, () => {
  // 重新设置搜索条件
  searchForm.value.executed_by_username = props.isInHomePage ? (storedCollectorName || '') : ''
  // 重新获取数据
  fetchDetails()
}, { immediate: false })
</script>

<template>
  <div class="detail-manage-container">
    <!-- 只在非首页模式下显示页面头部 -->
    <div v-if="!props.isInHomePage" class="page-header">
      <div class="breadcrumb">
        <span>您当前的位置：</span>
        <span class="location">数据采集系统</span>
        <span class="separator">/</span>
        <span class="current">采集结果</span>
      </div>
    </div>
    
    <div v-if="!props.isInHomePage" class="page-title">
      <h1>采集结果</h1>
    </div>
    
    <div class="page-content">
      <!-- 搜索面板 -->
      <div class="search-panel">
        <el-form 
          :model="searchForm" 
          inline 
          label-width="100px"
          class="search-form"
        >
          <el-row :gutter="20">
            <el-col :span="props.isInHomePage ? 8 : 6">
              <el-form-item label="需求名称">
                <el-input
                  v-model="searchForm.requirement_title"
                  placeholder="请输入需求名称"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="props.isInHomePage ? 8 : 6">
              <el-form-item label="指令描述">
                <el-input
                  v-model="searchForm.command_description"
                  placeholder="请输入指令描述"
                  clearable
                />
              </el-form-item>
            </el-col>
            <!-- 只在非首页模式下显示采集人搜索框 -->
            <el-col v-if="!props.isInHomePage" :span="6">
              <el-form-item label="采集人">
                <el-input
                  v-model="searchForm.executed_by_username"
                  placeholder="请输入采集人"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="props.isInHomePage ? 8 : 6">
              <el-form-item label=" ">
                <el-button type="primary" @click="handleSearch">
                  搜索
                </el-button>
                <el-button @click="handleReset">
                  重置
                </el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      
      <!-- 数据统计信息 -->
      <div class="data-summary" v-if="!loading">
        <span class="summary-text">
          共找到 <strong>{{ pagination.total }}</strong> 条采集结果
          <span v-if="hasSearchConditions" class="search-info">
            （搜索已激活）
          </span>
        </span>
      </div>
      
      <!-- 采集结果表格 -->
      <el-table
        :data="detailList"
        style="width: 100%"
        border
        :loading="loading"
        max-height="500"
      >
        <el-table-column
          type="index"
          label="序号"
          width="80"
          align="center"
        />
        
        <el-table-column
          prop="id"
          label="结果ID"
          min-width="240"
        >
          <template #default="scope">
            {{ scope.row.id || '暂无' }}
          </template>
        </el-table-column>
        
        <el-table-column
          prop="requirement_title"
          label="需求名称"
          min-width="150"
        >
          <template #default="scope">
            {{ scope.row.requirement_title || '暂无' }}
          </template>
        </el-table-column>
        
        <el-table-column
          prop="command_description"
          label="指令描述"
          min-width="200"
          show-overflow-tooltip
        >
          <template #default="scope">
            {{ scope.row.command_description || '暂无' }}
          </template>
        </el-table-column>
        
        <el-table-column
          prop="task_id"
          label="任务ID"
          min-width="100"
        >
          <template #default="scope">
            {{ scope.row.task_id || '暂无' }}
          </template>
        </el-table-column>
        
        <el-table-column
          prop="executed_by_username"
          label="采集人"
          min-width="100"
        >
          <template #default="scope">
            {{ scope.row.executed_by_username || '暂无' }}
          </template>
        </el-table-column>
        
        <el-table-column
          prop="execution_time"
          label="执行时间"
          min-width="160"
        >
          <template #default="scope">
            {{ scope.row.execution_time || '暂无' }}
          </template>
        </el-table-column>
        
        <el-table-column
          prop="status"
          label="状态"
          width="100"
          align="center"
        >
          <template #default="scope">
            <el-tag
              :type="scope.row.status === 'completed' ? 'success' : scope.row.status === 'running' ? 'warning' : scope.row.status === 'failed' ? 'danger' : 'info'"
            >
              {{ scope.row.status_display || scope.row.status || '未知' }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column
          prop="time_consumption"
          label="耗时(秒)"
          min-width="100"
          align="center"
        >
          <template #default="scope">
            {{ scope.row.time_consumption || '0' }}
          </template>
        </el-table-column>
        
        <el-table-column
          prop="machine_id"
          label="设备ID"
          min-width="100"
        >
          <template #default="scope">
            {{ scope.row.machine_id || '暂无' }}
          </template>
        </el-table-column>
        
        <el-table-column
          prop="machine_name"
          label="设备名称"
          min-width="120"
        >
          <template #default="scope">
            {{ scope.row.machine_name || '暂无' }}
          </template>
        </el-table-column>
        
        <!-- <el-table-column
          label="操作"
          width="150"
          fixed="right"
        >
          <template #default="scope">
            <el-button
              type="primary"
              link
              @click="openEditDialog(scope.row)"
            >
              编辑
            </el-button>
            <el-button
              type="danger"
              link
              @click="handleDelete(scope.row.id)"
            >
              删除
            </el-button>
          </template>
        </el-table-column> -->
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          background
        />
      </div>
    </div>
    
    <!-- 采集结果表单对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="600px"
      destroy-on-close
    >
      <el-form
        ref="detailFormRef"
        :model="detailForm"
        :rules="rules"
        label-width="100px"
        label-position="right"
      >
        <el-form-item label="需求名称" prop="requirement_title">
          <el-select 
            v-model="detailForm.requirement_title" 
            placeholder="请选择需求" 
            style="width: 100%"
            @change="handleRequirementChange"
            :loading="requirementsLoading"
          >
            <el-option
              v-for="item in requirementList"
              :key="item.id"
              :label="item.title"
              :value="item.title"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="指令描述" prop="command_description">
          <el-select 
            v-model="detailForm.command_description" 
            placeholder="请选择指令" 
            style="width: 100%"
            @change="handleCommandChange"
            :loading="commandsLoading"
          >
            <el-option
              v-for="item in commandList"
              :key="item.id"
              :label="item.description"
              :value="item.description"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="匹配任务" prop="task_id">
          <el-select 
            v-model="detailForm.task_id" 
            placeholder="请选择任务" 
            style="width: 100%"
            :loading="tasksLoading"
            :disabled="filteredTaskList.length === 0"
          >
            <el-option
              v-for="item in filteredTaskList"
              :key="item.id"
              :label="`任务${item.id} - 目标: ${item.target}`"
              :value="item.id"
            />
          </el-select>
          <div v-if="detailForm.requirement_title && detailForm.command_description && filteredTaskList.length === 0" 
               style="color: #f56c6c; font-size: 12px; margin-top: 4px;">
            未找到匹配的任务，请先在任务管理中创建对应的任务
          </div>
        </el-form-item>
        
        <el-form-item label="采集人" prop="executed_by_username">
          <el-input 
            v-model="detailForm.executed_by_username" 
            placeholder="采集人" 
            disabled
          />
        </el-form-item>
        
        <el-form-item label="状态" prop="status">
          <el-select v-model="detailForm.status" placeholder="请选择状态" style="width: 100%">
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="耗时(秒)" prop="time_consumption">
          <el-input 
            v-model.number="detailForm.time_consumption" 
            type="number" 
            placeholder="请输入耗时" 
          />
        </el-form-item>
        
        <el-form-item label="设备名称" prop="machine_name">
          <el-select v-model="detailForm.machine_name" placeholder="请选择设备" style="width: 100%" :loading="machinesLoading" @change="handleMachineChange">
            <el-option v-for="item in machineList" :key="item.id" :label="item.name" :value="item.name" />
          </el-select>
        </el-form-item>
        <el-form-item label="设备ID" prop="machine_id">
          <el-input v-model="detailForm.machine_id" placeholder="设备ID" readonly />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.detail-manage-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.breadcrumb {
  font-size: 14px;
  color: #606266;
  
  .location {
    color: #909399;
  }
  
  .separator {
    margin: 0 8px;
    color: #C0C4CC;
  }
  
  .current {
    color: #303133;
    font-weight: 500;
  }
}

.page-title {
  margin-bottom: 20px;
  
  h1 {
    font-size: 24px;
    color: #303133;
    margin: 0;
    font-weight: 600;
  }
}

.page-content {
  background-color: #fff;
  border-radius: 4px;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.operation-bar {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  
  .search-input {
    width: 300px;
  }
}

.data-summary {
  margin-bottom: 16px;
  padding: 12px 16px;
  background-color: #f8f9fa;
  border-left: 4px solid #409EFF;
  border-radius: 4px;
  
  .summary-text {
    font-size: 14px;
    color: #606266;
    
    strong {
      color: #409EFF;
      font-weight: 600;
    }
    
    .search-info {
      color: #909399;
      font-size: 13px;
      
      em {
        color: #E6A23C;
        font-style: normal;
        font-weight: 500;
      }
    }
  }
}

.search-panel {
  margin-bottom: 10px;
  padding: 8px 4px;
  // background-color: #f8f9fa;
  // border-radius: 6px;
  // border: 1px solid #e9ecef;
  
  .search-form {
    .el-form-item {
      margin-bottom: 16px;
      
      .el-input {
        width: 100%;
      }
    }
    
    .el-form-item__label {
      font-weight: 500;
      color: #606266;
    }
  }
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style> 