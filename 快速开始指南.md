# Lion Rock 数据收集控制系统 - 简化操作流程

## 🚀 核心操作流程

```
登录系统 → 设备管理 → 任务配置 → 开始采集 → 实时监控 → 停止采集 → 查看结果
```

---

## 📋 详细操作步骤

### 1. 登录系统
```
访问系统地址 → 输入用户名 → 点击登录 → 进入主页面
```

### 2. 设备管理
```
点击"设备管理" → 点击"新建设备" → 填写设备信息 → 保存设备 → 验证连接状态
```
**关键信息：**
- 设备名称（如：GO2_Robot_01）
- IP地址（如：*************）
- 端口（如：7001）

### 3. 任务配置
```
创建需求 → 创建指令 → 组合任务
```

#### 3.1 需求管理
```
点击"需求管理" → 点击"新建需求" → 填写需求信息 → 保存
```

#### 3.2 指令管理  
```
点击"指令管理" → 点击"新建指令" → 填写指令信息 → 保存
```

#### 3.3 任务管理
```
点击"任务管理" → 点击"新建任务" → 选择需求和指令 → 设置参数 → 创建任务
```

### 4. 开始采集
```
返回主页 → 选择设备 → 激活设备 → 选择需求和指令 → 检查传感器状态 → 点击"开始采集"
```

### 5. 实时监控
```
查看采集状态 → 监控传感器数据 → 观察实时图表 → 检查日志信息
```

### 6. 停止采集
```
点击"停止采集" → 等待数据保存 → 确认采集结果 → 记录采集ID
```

### 7. 查看结果
```
点击"详情管理" → 查看采集记录 → 下载数据文件
```

---

## 🎯 关键操作要点

### ✅ 开始前检查
- [ ] 网络连接正常
- [ ] 设备状态在线
- [ ] 传感器状态正常（绿色）
- [ ] 已选择需求和指令

### 🔍 采集过程监控
- [ ] 采集状态显示"运行中"
- [ ] 实时数据正常更新
- [ ] 相机图像清晰
- [ ] IMU数据稳定

### 📊 结果验证
- [ ] 采集时长符合预期
- [ ] 数据验证状态为"成功"
- [ ] 文件大小合理
- [ ] 可以正常下载

---

## ⚠️ 常见问题快速解决

| 问题现象 | 快速解决 |
|---------|----------|
| 设备显示离线 | 检查IP地址和端口 |
| 传感器状态红色 | 重新连接设备 |
| 无法开始采集 | 检查需求和指令选择 |
| 数据验证失败 | 重新采集数据 |
| 无法下载文件 | 检查网络和权限 |

---

## 🏃‍♂️ 快速上手（5分钟）

### 第一次使用最小流程：
1. **登录** → 输入用户名
2. **添加设备** → 填写IP和端口
3. **选择预设** → 使用默认需求和指令
4. **开始采集** → 直接点击开始按钮
5. **查看结果** → 采集完成后查看数据

### 示例配置：
```
用户名：test_user
设备IP：*************:7001
需求：室内场景采集
指令：测试模式
```

---

*此流程适合配合界面截图进行详细说明，每个步骤可对应相应的操作界面图片。*