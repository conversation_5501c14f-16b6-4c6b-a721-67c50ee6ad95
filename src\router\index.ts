import { createRouter, createWeb<PERSON>ash<PERSON><PERSON><PERSON>, RouteRecordRaw } from 'vue-router'
import HomeView from '../views/HomeView/HomeView.vue'
import LoginView from '../views/LoginView/LoginView.vue'
import UserManageView from '../views/UserManageView/UserManageView.vue'
import RequirementManage from '../views/RequirementManage/RequirementManage.vue'
import CommandView from '../views/CommandView/CommandView.vue'
import TaskManage from '../views/TaskManage/TaskManage.vue'
import DetailManage from '../views/DetailManage/DetailManage.vue'
import MachineManage from '../views/MachineManage/MachineManage.vue'

const routes: Array<RouteRecordRaw> = [
  {
    path: '/login',
    name: 'login',
    component: LoginView
  },
  {
    path: '/',
    name: 'home',
    component: HomeView,
    meta: { requiresAuth: true }
  },
  {
    path: '/user-manage',
    name: 'userManage',
    component: UserManageView,
    meta: { requiresAuth: true }
  },
  {
    path: '/requirement-manage',
    name: 'requirementManage',
    component: RequirementManage,
    meta: { requiresAuth: true }
  },
  {
    path: '/command-view',
    name: 'commandView',
    component: CommandView,
    meta: { requiresAuth: true }
  },
  {
    path: '/task-manage',
    name: 'taskManage',
    component: TaskManage,
    meta: { requiresAuth: true }
  },
  {
    path: '/machine-manage',
    name: 'machineManage',
    component: MachineManage,
    meta: { requiresAuth: true }
  },
  {
    path: '/detail-manage',
    name: 'detailManage',
    component: DetailManage,
    meta: { requiresAuth: true }
  }
]

const router = createRouter({
  history: createWebHashHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  console.log('从:', from.path)
  console.log('到:', to.path) // 更清晰的日志
  const collectorName = localStorage.getItem('collectorName')
  
  // 如果访问需要认证的页面但未登录，跳转到登录页
  if (to.meta.requiresAuth && !collectorName) {
    console.log('未登录，跳转到登录页')
    next('/login')
  }
  // 如果已登录但访问登录页，跳转到首页
  else if (to.name === 'login' && collectorName) {
    console.log('已登录，从登录页跳转到首页')
    next('/')
  }
  // 其他情况正常跳转
  else {
    console.log('正常跳转到:', to.path)
    next()
  }
})

export default router
