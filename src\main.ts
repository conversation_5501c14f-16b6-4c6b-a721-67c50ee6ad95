import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import './styles/scrollbar.css'
import type { TRROSDK, TRROSDKParams } from './types/trro-sdk'

// 导入腾讯 POC SDK
let TRROSDKClass: typeof TRROSDK | null = null;

// 动态加载 TRRO SDK
async function loadTRROSDK() {
  try {
    // 从 public 目录加载 SDK
    const script = document.createElement('script');
    script.src = '/trro/index.js';
    script.async = true;

    return new Promise((resolve, reject) => {
      script.onload = () => {
        // SDK 加载完成后，从 window 对象获取 TRROSDK
        if (window.TRROSDK) {
          TRROSDKClass = window.TRROSDK;
          console.log('TRRO SDK 加载成功:', TRROSDKClass);
          resolve(TRROSDKClass);
        } else {
          reject(new Error('TRRO SDK 未找到'));
        }
      };

      script.onerror = () => {
        reject(new Error('TRRO SDK 加载失败'));
      };

      document.head.appendChild(script);
    });
  } catch (error) {
    console.error('加载 TRRO SDK 时出错:', error);
    throw error;
  }
}

// 创建 TRRO SDK 实例的函数
function createTRROSDKInstance(params: TRROSDKParams) {
  if (!TRROSDKClass) {
    throw new Error('TRRO SDK 尚未加载');
  }

  try {
    const instance = new TRROSDKClass(params);
    return instance;
  } catch (error) {
    throw error;
  }
}

// 将 TRRO SDK 相关功能挂载到全局
const app = createApp(App)

// 在应用启动前加载 SDK
loadTRROSDK().then(() => {
  try {
    // 公有云模式示例
    const testInstancePublic = createTRROSDKInstance({
      cloudMode: 'public',
      projectId: '7pr5mlov95ys9h1r',
      remoteDeviceId: 'laptop',
      password: '1234123412341234'
    });
    console.log('公有云模式测试实例:', testInstancePublic);

  } catch (error) {
    console.error('创建测试实例失败:', error);
  }
}).catch((error) => {
  console.error('TRRO SDK 加载失败:', error);
});

// 将 TRRO SDK 相关功能添加到全局属性
app.config.globalProperties.$TRROSDK = TRROSDKClass;
app.config.globalProperties.$createTRROSDKInstance = createTRROSDKInstance;

app.use(ElementPlus)
app.use(router)
app.mount('#app')