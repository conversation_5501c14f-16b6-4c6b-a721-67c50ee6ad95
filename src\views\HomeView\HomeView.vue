<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { ElButton, ElCard, ElMessage, ElMessageBox, ElDropdown, ElDropdownMenu, ElDropdownItem, ElTooltip } from 'element-plus'
import { useRouter } from 'vue-router'
// import { getClientLogsApi } from '@/common/request/api/getClientLogs'
// import { clearClientLogsApi } from '@/common/request/api/clearClientLogs'
import { downloadImage, downloadBinary, formatBytes } from '@/common/utils/fileUtils'
import { formatTimestamp, generateUUID } from '@/common/utils/timeUtils'
import { getRequirementsApi } from '@/common/request/api/requirementInfo'
import { getCommandsApi, createCommandApi } from '@/common/request/api/commandInfo'
import { getTasksApi, createTaskApi } from '@/common/request/api/tasksInfo'
import { getMachinesApi } from '@/common/request/api/machineInfo'
import useHomeView from './homeView'
import ROSLIB from 'roslib'
import * as echarts from 'echarts'
import { createDetailApi } from '@/common/request/api/detailsInfo'
import CommandDialog from './components/CommandDialog.vue'
import DetailManage from '../DetailManage/DetailManage.vue'

const router = useRouter()
const data = useHomeView()
const dialogVisible = ref(false)
// 添加需求列表和指令列表的响应式变量
const requirementList = ref<any[]>([])
const commandList = ref<any[]>([])
const requirementsLoading = ref(false)
const commandsLoading = ref(false)
const uuid = ref('')
// 添加设备列表和加载状态
const machineList = ref<any[]>([])
const machinesLoading = ref(false)
const verificationVisible = ref(false)
const handleConfirm = async () => {
  console.log('handleConfirm')
  try {
    await createDetailRecord('success')
    // 只有在API调用成功后才显示成功消息并关闭对话框
    ElMessage.success('采集成功')
    verificationVisible.value = false
  } catch (error) {
    // 如果API调用失败，保持对话框打开，让用户可以重试
    console.error('确认操作失败:', error)
    ElMessage.error('操作失败，请重试')
  }
}

const handleCancel = () => {
  console.log('handleCancel')
}


// 获取需求列表
const fetchRequirements = async () => {
  try {
    requirementsLoading.value = true
    const res = await getRequirementsApi()
    
    if (Array.isArray(res.data)) {
      requirementList.value = res.data
      console.log('获取到的需求列表:', requirementList.value)
      
      // 更新data.requirementOptions
      data.requirementOptions.value = res.data.map((item: any) => ({
        value: item.id,
        label: item.title
      }))
    } else {
      throw new Error('需求接口返回数据格式异常')
    }
  } catch (error: any) {
    console.error('获取需求列表失败:', error)
    ElMessage.error(`获取需求列表失败: ${error.message || '未知错误'}`)
  } finally {
    requirementsLoading.value = false
  }
}

// 获取指令列表
const fetchCommands = async () => {
  try {
    commandsLoading.value = true
    const res = await getCommandsApi()
    
    if (Array.isArray(res.data)) {
      commandList.value = res.data
      
      // 更新data.instructionOptions
      data.instructionOptions.value = res.data.map((item: any) => ({
        value: item.id,
        label: item.description
      }))
    } else {
      throw new Error('指令接口返回数据格式异常')
    }
  } catch (error: any) {
    console.error('获取指令列表失败:', error)
    ElMessage.error(`获取指令列表失败: ${error.message || '未知错误'}`)
  } finally {
    commandsLoading.value = false
  }
}

// 获取设备列表
const fetchMachines = async () => {
  try {
    machinesLoading.value = true
    const res = await getMachinesApi()
    
    if (Array.isArray(res.data)) {
      machineList.value = res.data
      
      // 更新data.clientOptions，使用设备的IP地址和端口
      data.clientOptions.value = res.data.map((item: any) => {
        const clientAddress = item.port ? `${item.ip_address}:${item.port}` : item.ip_address
        return {
          value: clientAddress,
          label: `${item.name} (${clientAddress})`
        }
      }).filter((item: any) => item.value) // 过滤掉没有IP地址的设备
      console.log('获取到的设备列表:', data.clientOptions.value)
    } else {
      throw new Error('设备接口返回数据格式异常')
    }
  } catch (error: any) {
    console.error('获取设备列表失败:', error)
    ElMessage.error(`获取设备列表失败: ${error.message || '未知错误'}`)
  } finally {
    machinesLoading.value = false
  }
}

// 从useHomeView中获取ROS相关状态和其他状态管理变量
// 这些变量现在通过data对象访问

// 获取客户端显示标签的辅助函数
const getClientLabel = (clientId: string) => {
  const client = data.clientOptions.value.find(option => option.value === clientId)
  return client ? client.label : clientId
}

// 添加日志到界面日志框
function addLog(message: string | any, type: 'success' | 'error' | 'info' = 'info') {
  const timestamp = Math.floor(Date.now() / 1000);
  const date = new Date();
  const formattedTime = date.toLocaleString();
  
  // 根据类型设置不同的日志类型标签
  let logType = 'text';
  
  // 处理非字符串类型的消息
  let messageText: string;
  let originalMessage: any = null;
  
  // 调试：打印传入的消息
  console.log('addLog called with:', { message, type, messageType: typeof message });
  
  if (typeof message === 'string') {
    messageText = message;
  } else if (message && typeof message === 'object') {
    try {
      // 特殊处理camera_color_optical_frame类型图像数据
      if (message.header && message.header.frame_id === 'camera_color_optical_frame' && message.format && message.format.includes('jpeg')) {
        // 标记为图像类型
        logType = 'json';
        // 不显示图像数据的具体内容，而是显示摘要
        const dataSummary = { ...message };
        if (dataSummary.data && typeof dataSummary.data === 'string') {
          dataSummary.data = '[图像数据已隐藏]';
        }
        messageText = JSON.stringify(dataSummary, null, 2);
        originalMessage = message; // 保留原始消息以供后续处理
      } else if (message.header && message.header.frame_id) {
        // 处理IMU传感器数据
        logType = 'json';
        originalMessage = message;
        
        // 为IMU数据创建友好的摘要
        let summaryText = '';
        if (message.linear_acceleration) {
          summaryText = `加速度传感器数据 - 线性加速度: X=${message.linear_acceleration.x.toFixed(3)}, Y=${message.linear_acceleration.y.toFixed(3)}, Z=${message.linear_acceleration.z.toFixed(3)}`;
        } else if (message.angular_velocity) {
          summaryText = `陀螺仪传感器数据 - 角速度: X=${message.angular_velocity.x.toFixed(3)}, Y=${message.angular_velocity.y.toFixed(3)}, Z=${message.angular_velocity.z.toFixed(3)}`;
        } else {
          summaryText = `ROS消息 - frame_id: ${message.header.frame_id}`;
        }
        
        messageText = summaryText;
      } else {
        // 保存原始对象，用于可折叠显示
        originalMessage = message;
        // 仅字符串化显示用
        messageText = JSON.stringify(message, null, 2);
        logType = 'json';
      }
    } catch (e) {
      messageText = `无法格式化对象消息: ${e}`;
      console.error('Error formatting message:', e, message);
    }
  } else if (message === null || message === undefined) {
    messageText = '收到空消息';
  } else {
    messageText = String(message);
  }
  
  // 确保messageText不为空
  if (!messageText || messageText.trim() === '') {
    messageText = '未知消息内容';
  }
  
  // 添加新日志到数组头部（最新的显示在前面）
  const logEntry = {
    message: messageText,
    originalMessage: originalMessage,  // 添加原始对象引用
    timestamp: timestamp,
    formatted_time: formattedTime,
    type: logType,
    log_level: type, // 保存日志级别，可用于显示不同颜色
    client_id: data.selectedClientId.value || '',
    client_name: data.selectedClientId.value ? `客户端${data.selectedClientId.value}` : '系统',
    expanded: false  // 默认不展开
  };
  
  console.log('Adding log entry:', logEntry);
  data.logs.value.unshift(logEntry);
  
  // 如果启用了自动滚动
  if (data.autoScroll.value) {
    scrollLogsToTop();
  }
}

// 选择客户端
const selectClient = (clientId: string) => {
  // 自定义需求时，创建任务
  // if(data.collectionInfo.requirement === 39) {
  //   data.handleCustomInstruction(data.collectionInfo.requirement)
  // }
  data.selectedClientId.value = clientId;
  console.log('%c [ selectedClientId.value ]-67', 'font-size:13px; background:#e38d26; color:#ffd16a;', data.selectedClientId.value)

  // 如果有选中的客户端，则连接
  if (data.selectedClientId.value) {
    // 连接到选中的客户端
    connectToClient(data.selectedClientId.value);
    
    // 根据当前ROS连接状态初始化通知
    if (!data.isRosConnected.value) {
      addNotification('warning', '正在连接到客户端设备...');
    }
  } else {
    // 取消订阅并断开连接
    unsubscribeAll();
    disconnect(); // 内部切换，不显示通知
    data.activeClientId.value = '';  // 使用空字符串而不是 null

    // 重置重连状态和停止任何正在进行的重连
    reconnectAttempts.value = 0;
    if (reconnectTimer) {
      clearTimeout(reconnectTimer);
      reconnectTimer = null;
    }
    
    // 清空所有通知
    notifications.value = [];
    stopNotificationTicker();
  }
}

// 添加刷新状态变量
const isRefreshing = ref(false);

const refreshConect = () => {
  if (data.activeClientId.value) {
    // 设置刷新状态
    isRefreshing.value = true;
    
    // 刷新连接
    buildLink(data.activeClientId.value);
    
    // 设置超时，确保即使连接过程很快也能看到动画效果
    setTimeout(() => {
      // 检查采集需求和采集指令状态，整合提示信息
      let missingInfo = [];
      if (!data.collectionInfo.requirement) {
        missingInfo.push("采集需求");
      }
      
      if (!data.collectionInfo.instruction) {
        missingInfo.push("采集指令");
      }
      
      if (missingInfo.length > 0) {
        ElMessage({
          message: `连接已刷新，但${missingInfo.join("和")}尚未选择`,
          type: "warning",
          duration: 3000
        });
      } else {
        ElMessage({
          message: "连接已刷新，采集需求和采集指令已准备就绪",
          type: "success",
          duration: 3000
        });
      }
      
      // 重置刷新状态
      isRefreshing.value = false;
    }, 500);
  } else {
    ElMessage.warning("请先选择一个客户端");
  }
}

// 连接到指定客户端
const connectToClient = (clientId: string) => {
  // 如果已连接到该客户端，则不重新连接
  if (data.activeClientId.value === clientId && data.isRosConnected.value) {
    console.log(`已连接到客户端 ${clientId}，跳过重复连接`);
    return;
  }
  
  // 如果已连接到其他客户端，先取消订阅并断开
  if (data.isRosConnected.value) {
    console.log(`断开当前客户端 ${data.activeClientId.value}，连接到新客户端 ${clientId}`);
    unsubscribeAll();
    disconnect(); // 内部切换，不显示通知
  }
  
  console.log(`开始连接到客户端: ${clientId}`);
  data.activeClientId.value = clientId;
  data.activeClientMachineId.value = machineList.value.find(machine => machine.ip_address+':'+ machine.port === clientId)?.id
  buildLink(clientId);
}

// 切换当前活跃的客户端
// const switchActiveClient = (clientId: string) => {
//   if (clientId === data.activeClientId.value) return;
  
//   connectToClient(clientId);
//   ElMessage.success(`已切换至客户端: ${clientId}`);
// }

// 启动录制
function startRecording(bagFilename: string): Promise<any> {
  return new Promise((resolve, reject) => {
    try {
      if (!data.isRosConnected.value || !data.ros.value) {
        const errorMsg = "ROS 连接未建立，请等待连接...";
        addLog(errorMsg, 'error');
        reject(errorMsg);
        return;
      }
      console.log('data.ros.value', data.ros.value)
      
      // 获取需求和指令的描述
      const requirementOption = data.requirementOptions.value.find(opt => opt.value === data.collectionInfo.requirement)
      const instructionOption = data.instructionOptions.value.find(opt => opt.value === data.collectionInfo.instruction)
      
      const requirementDescription = requirementOption ? requirementOption.label : data.collectionInfo.requirement
      const instructionDescription = instructionOption ? instructionOption.label : data.collectionInfo.instruction
      
      // 获取机器名称
      const machineOption = machineList.value.find(machine => machine.id === data.activeClientMachineId.value)
      const machineName = machineOption ? machineOption.name : data.activeClientMachineId.value
      
      const detailPayload = {
        offline_mode: false,
        id: uuid.value,
        task_id: currentTaskId.value,
        executed_by_username: data.collectorName.value,
        time_consumption: data.collectDuration.value,
        machine_id: data.activeClientMachineId.value,
        machine_name: machineName,
        requirement_id: data.collectionInfo.requirement,
        requirement_description: requirementDescription,
        command_id: data.collectionInfo.instruction,
        command_description: instructionDescription
      }
      console.log('detailPayload', JSON.stringify(detailPayload))
      const startService = new (ROSLIB as any).Service({
        ros: data.ros.value,
        name: '/start_recording',
        serviceType: 'rosbag_service/StartRecording'
      });
      const startRequest = new (ROSLIB as any).ServiceRequest({
        bag_filename: bagFilename,
        details: JSON.stringify(detailPayload)
      });

      startService.callService(startRequest, (response: any) => {
        console.log('/start_recording response:', response);
        const successMsg = `录制已启动: ${response.message || "成功"}`;
        addLog(successMsg, response.success ? 'success' : 'error');
        
        // 启动录制后，获取最新状态
        getRecordingStatus().catch(error => {
          console.error("获取启动后状态失败:", error);
        });
        
        resolve(response);
      }, (error: Error) => {
        console.error('/start_recording error:', error);
        const errorMsg = `启动录制失败: ${error.toString()}`;
        addLog(errorMsg, 'error');
        reject(error);
      });
    } catch (error) {
      const errorMsg = `启动录制发生异常: ${error instanceof Error ? error.message : String(error)}`;
      addLog(errorMsg, 'error');
      reject(error);
    }
  });
}

// 开始采集
const startCollect = async () => {
  // 检查采集信息是否完整
  if (!data.collectionInfo.requirement) {
    ElMessage.warning("请先选择采集需求!")
    return
  }
  
  if (!data.collectionInfo.instruction) {  
    ElMessage.warning("请先选择采集指令!")
    return
  }
  
  // 检查是否有选中的客户端
  if (!data.selectedClientId.value) {
    ElMessage.warning("请先选择一个客户端!")
    return
  }
  
  // 检查当前活跃客户端
  if (!data.activeClientId.value) {
    ElMessage.warning("请先激活一个客户端!")
    return
  }
  
  // 检查 ROS 连接状态
  if (!data.isRosConnected.value) {
    const errorMsg = "ROS 连接未建立，请等待连接..."
    ElMessage.error(errorMsg)
    addLog(errorMsg, 'error')
    return
  }

  data.startLoading.value = true // 开始loading
  
  try {
    // 检测传感器状态
    await checkSensors()
    
    // 检查是否所有传感器都可用
    const currentStatus = data.sensorStatus.value[data.activeClientId.value!]
    if (!currentStatus.hasColor) {
      const missingTypes = []
      if (!currentStatus.hasColor) missingTypes.push('相机')
      
      addLog(`缺少传感器数据：${missingTypes.join('、')}`, 'error')
    }
    
    // 创建一个Date对象并固定使用它，确保所有时间部分使用相同的时间点
    // const now = new Date()
    
    // 确保生成严格17位的日期时间字符串（包含毫秒）
    // let datePart = [
    //   String(now.getFullYear()),  // 4位年份
    //   String(now.getMonth() + 1).padStart(2, '0'),  // 2位月份，补0
    //   String(now.getDate()).padStart(2, '0'),  // 2位日期，补0
    //   String(now.getHours()).padStart(2, '0'),  // 2位小时，补0
    //   String(now.getMinutes()).padStart(2, '0'),  // 2位分钟，补1
    //   String(now.getSeconds()).padStart(2, '0'),  // 2位秒数，补1
    //   String(now.getMilliseconds()).padStart(3, '1')  // 3位毫秒，补0
    // ].join('')
    
    // 生成UUID
    uuid.value = ''
    uuid.value = generateUUID()
    // 严格验证长度，确保是17位
    // if (datePart.length !== 17) {
    //   console.error('警告: 生成的日期格式不是17位!', datePart)
    //   // 如果长度不是17位，强制使用当前时间戳后17位作为备用
    //   const timestamp = String(Date.now()) + String(now.getMilliseconds()).padStart(3, '0')
    //   datePart = timestamp.substring(timestamp.length - 17)
    // }
    const bagFilename = `/${uuid.value}`
    
    // 记录采集信息
    // 根据ID获取需求和指令的名称/描述
    const requirementOption = data.requirementOptions.value.find(opt => opt.value === data.collectionInfo.requirement)
    const instructionOption = data.instructionOptions.value.find(opt => opt.value === data.collectionInfo.instruction)
    
    const requirementLabel = requirementOption ? requirementOption.label : data.collectionInfo.requirement
    const instructionLabel = instructionOption ? instructionOption.label : data.collectionInfo.instruction

    addLog(`开始采集任务 - 采集人: ${data.collectorName.value}, 需求: ${requirementLabel}, 指令: ${instructionLabel}`, 'info')
    
    // await addTopic()
    ElMessage.success("所有Topic添加成功")
    addLog("所有Topic添加成功，准备开始录制", 'success')
    
    // 所有Topic添加完成后，启动录制
    await startRecording(bagFilename)
    ElMessage.success("ROS 已开始录制")

    // 启动运行时长计时器
    startDurationTimer()
    
    // 注意：传感器数据订阅已在ROS连接成功时启动，无需重复调用

    // 自定义需求时，创建任务
    if(data.collectionInfo.requirement === 39) {
      data.handleCustomInstruction(data.collectionInfo.requirement)
    }
  } catch (error) {
    console.error("任务启动失败:", error)
    const errorMsg = "启动失败: " + (error instanceof Error ? error.message : String(error))
    ElMessage.error(errorMsg)
    addLog(errorMsg, 'error')
  } finally {
    data.startLoading.value = false // 结束loading
  }
}

// 添加显示模式状态
const logDisplayMode = ref('dashboard') // 'log' 或 'dashboard'

// 添加运行时长计算相关变量
const collectStartTime = ref<number | null>(null)
const collectDuration = ref<string>('00:00:00')
let durationTimer: NodeJS.Timeout | null = null

// 格式化运行时长
const formatDuration = (seconds: number): string => {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = seconds % 60
  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
}

// 更新运行时长
const updateDuration = () => {
  if (collectStartTime.value && data.collectStatus.value === '运行中') {
    const now = Date.now()
    const elapsed = Math.floor((now - collectStartTime.value) / 1000)
    collectDuration.value = formatDuration(elapsed)
  }
}

// 开始计时
const startDurationTimer = () => {
  collectStartTime.value = Date.now()
  collectDuration.value = '00:00:00'

  // 每秒更新一次
  durationTimer = setInterval(updateDuration, 1000)
}

// 停止计时
const stopDurationTimer = () => {
  if (durationTimer) {
    clearInterval(durationTimer)
    durationTimer = null
  }
  collectStartTime.value = null
}

// 自定义指令模式相关状态
const isCustomMode = ref(false)
const customInstructionInput = ref('')
const isCreatingCustomTask = ref(false)

// 侧边栏折叠状态
const isSidebarCollapsed = ref(false)

// 全屏相机模式状态
const isFullscreenCamera = ref(false)
// 全屏模式下侧边栏overlay显示状态
const isFullscreenSidebarVisible = ref(false)

// 切换侧边栏折叠状态
const toggleSidebar = () => {
  isSidebarCollapsed.value = !isSidebarCollapsed.value
}

// 保存进入全屏前的侧边栏状态
const sidebarStateBeforeFullscreen = ref(false)

// 切换全屏相机模式
const toggleFullscreenCamera = () => {
  if (!isFullscreenCamera.value) {
    // 进入全屏模式 - 保存当前侧边栏状态，并隐藏侧边栏
    sidebarStateBeforeFullscreen.value = isSidebarCollapsed.value
    isFullscreenSidebarVisible.value = false
  } else {
    // 退出全屏模式 - 恢复之前的侧边栏状态
    isSidebarCollapsed.value = sidebarStateBeforeFullscreen.value
    isFullscreenSidebarVisible.value = false
  }
  
  isFullscreenCamera.value = !isFullscreenCamera.value
}

// 切换全屏模式下的侧边栏显示
// const toggleFullscreenSidebar = () => {
//   isFullscreenSidebarVisible.value = !isFullscreenSidebarVisible.value
// }

// 处理箭头按钮点击
const handleArrowClick = () => {
  isFullscreenSidebarVisible.value = !isFullscreenSidebarVisible.value
}



// 通知滚动提示系统
interface Notification {
  type: 'info' | 'error' | 'warning' | 'success'
  message: string
}

const notifications = ref<Notification[]>([])

const currentNotification = ref({} as any)
const notificationIndex = ref(0)
const notificationTimer = ref({} as any)

// 启动通知轮播 - 只有在选择了客户端且有多个通知时才启动
const startNotificationTicker = () => {
  if (!data.selectedClientId.value || notifications.value.length <= 1) {
    // 如果没有客户端或只有一个通知，不需要轮播
    return
  }
  
  // 停止之前的定时器
  stopNotificationTicker()
  
  notificationTimer.value = setInterval(() => {
    notificationIndex.value = (notificationIndex.value + 1) % notifications.value.length
  }, 3000) // 每3秒切换一次
}

// 停止通知轮播
const stopNotificationTicker = () => {
  if (notificationTimer.value) {
    clearInterval(notificationTimer.value)
    notificationTimer.value = null
  }
}

// 添加新通知
const addNotification = (type: 'info' | 'error' | 'warning' | 'success', message: string) => {
  // 避免重复添加相同的通知
  const existingNotification = notifications.value.find(n => n.message === message)
  if (existingNotification) {
    console.log(`通知已存在，跳过添加: ${message}`)
    return
  }
  
  const newNotification: Notification = { type, message }
  notifications.value.push(newNotification)
  console.log(`添加通知: [${type}] ${message}`)
  
  // 通知变化会由watch监听器自动处理轮播启停
}

// 移除通知
const removeNotification = (message: string) => {
  const index = notifications.value.findIndex(n => n.message === message)
  if (index > -1) {
    notifications.value.splice(index, 1)
    console.log(`移除通知: ${message}`)
    
    // 如果删除的是当前显示的通知，切换到下一个
    if (notifications.value.length === 0) {
      currentNotification.value = null
      stopNotificationTicker()
    } else if (currentNotification.value?.message === message) {
      notificationIndex.value = notificationIndex.value % notifications.value.length
      currentNotification.value = notifications.value[notificationIndex.value]
    }
  } else {
    console.log(`未找到要移除的通知: ${message}`)
  }
}

// 处理自定义指令输入框失焦
const handleCustomInstructionBlur = (val: any) => {
  if (!val.target.value?.trim()) {
    data.collectionInfo.instruction = ''
  }
}

// 监听自定义模式开关的变化
watch(isCustomMode, (newValue) => {
  if (!newValue) {
    // 切换回下拉选择模式，清空自定义输入
    customInstructionInput.value = ''
  } else {
    // 切换到自定义模式，清空当前选择
    data.collectionInfo.instruction = ''
  }
})

// 监听客户端选择状态变化，管理通知轮播
watch(() => data.selectedClientId.value, (newClientId, oldClientId) => {
  console.log('oldClientId', oldClientId)
  if (newClientId && notifications.value.length > 1) {
    // 选择了客户端且有多个通知，启动轮播
    startNotificationTicker()
  } else {
    // 未选择客户端或只有一个通知，停止轮播
    stopNotificationTicker()
  }
})

// 监听通知数组变化，管理轮播启停
watch(() => notifications.value.length, (newLength, oldLength) => {
  console.log('oldLength', oldLength)
  if (data.selectedClientId.value) {
    if (newLength > 1) {
      // 客户端已选择，且有多个通知，启动轮播
      startNotificationTicker()
    } else {
      // 只有一个或没有通知，停止轮播
      stopNotificationTicker()
      // 重置索引到第一个
      notificationIndex.value = 0
    }
  }
})

// 取消自定义指令
const cancelCustomInstruction = () => {
  customInstructionInput.value = ''
  isCustomMode.value = false
}

// 确认自定义指令，创建指令和任务
const confirmCustomInstruction = async () => {
  if (!customInstructionInput.value.trim()) {
    ElMessage.warning('请输入自定义指令内容')
    return
  }
  
  if (!data.collectionInfo.requirement) {
    ElMessage.warning('请先选择采集需求')
    return
  }

  try {
    isCreatingCustomTask.value = true
    
    // 获取当前登录用户信息
    const storedCollectorName = localStorage.getItem('collectorName')
    
    // 第一步：创建自定义指令
    const createCommandData = {
      description: customInstructionInput.value.trim(),
      created_by_username: storedCollectorName
    }
    
    console.log('创建自定义指令:', createCommandData)
    const commandResult = await createCommandApi(createCommandData)
    console.log('指令创建结果:', commandResult)
    
    // 第二步：创建任务
    const createTaskData = {
      requirement_id: [data.collectionInfo.requirement],
      command_id: [commandResult.data.id],
    }
    
    console.log('创建任务:', createTaskData)
    const taskResult = await createTaskApi(createTaskData)
    console.log('任务创建结果:', taskResult)
    
    // 第三步：设置当前选中的指令
    data.collectionInfo.instruction = commandResult.data.id
    
    // 第四步：刷新指令列表，添加新创建的指令到选项中
    const newOption = {
      value: commandResult.data.id,
      label: customInstructionInput.value.trim()
    }
    data.instructionOptions.value.push(newOption)
    
    // 成功提示
    ElMessage.success('自定义指令和任务创建成功')
    addLog(`自定义指令创建成功: ${customInstructionInput.value.trim()}`, 'success')
    
    // 重置状态
    customInstructionInput.value = ''
    isCustomMode.value = false
    
  } catch (error: any) {
    console.error('创建自定义指令失败:', error)
    
    // 处理指令已存在的情况
    if (error.response && error.response.data && error.response.data.message === '采集指令已存在') {
      try {
        // 搜索已存在的指令
        console.log('指令已存在，开始搜索指令ID...', customInstructionInput.value.trim())
        const searchResult = await getCommandsApi({ description: customInstructionInput.value.trim() })
        console.log('搜索指令结果:', searchResult)
        
        if (searchResult.status === 'success' && searchResult.data && searchResult.data.length > 0) {
          const existingCommand = searchResult.data[0]
          console.log('找到已存在的指令ID:', existingCommand.id)
          
          // 使用已存在的指令创建任务
          const createTaskData = {
            requirement_id: [data.collectionInfo.requirement],
            command_id: [existingCommand.id],
          }
          
          const taskResult = await createTaskApi(createTaskData)
          console.log('使用已存在指令创建任务成功:', taskResult)
          
          // 设置当前选中的指令
          data.collectionInfo.instruction = existingCommand.id
          
          // 确保指令在选项列表中
          const existingOption = data.instructionOptions.value.find(opt => opt.value === existingCommand.id)
          if (!existingOption) {
            data.instructionOptions.value.push({
              value: existingCommand.id,
              label: existingCommand.description
            })
          }
          
          ElMessage.success('使用已存在指令创建任务成功')
          addLog(`使用已存在指令: ${existingCommand.description}`, 'success')
          
          // 重置状态
          customInstructionInput.value = ''
          isCustomMode.value = false
        } else {
          throw new Error('未找到匹配的指令')
        }
      } catch (searchError: any) {
        console.error('搜索已存在指令失败:', searchError)
        ElMessage.error(`操作失败: ${searchError.message || '未知错误'}`)
      }
    } else {
      ElMessage.error(`创建失败: ${error.message || '未知错误'}`)
    }
  } finally {
    isCreatingCustomTask.value = false
  }
}

// 添加IMU数据类型定义
// interface Vector3 {
//   x: number;
//   y: number;
//   z: number;
// }

// interface Quaternion extends Vector3 {
//   w: number;
// }

// interface ImuData {
//   header: {
//     seq: number;
//     stamp: {
//       secs: number;
//       nsecs: number;
//     };
//     frame_id: string;
//   };
//   orientation: Quaternion;
//   orientation_covariance: number[];
//   angular_velocity: Vector3;
//   angular_velocity_covariance: number[];
//   linear_acceleration: Vector3;
//   linear_acceleration_covariance: number[];
// }

// 修改数据引用的类型
const imuData = ref<any | null>(null);
const latestCameraImage = ref<string | null>(null)
const cameraInfo = ref({
  width: null,
  height: null,
  fps: 0,
  format: null,
  frameCount: 0,
  lastFrameTime: Date.now()
})

// 图表实例
let accelChart: any = null
let gyroChart: any = null
let trajectoryChart: any = null

// 轨迹数据管理
const trajectoryData = ref<Array<{x: number, y: number, timestamp: number}>>([])
const isTrajectoryRecording = ref(true)
const maxTrajectoryPoints = ref(1000) // 最大轨迹点数，避免内存过载

// 检查当前订阅状态
function checkSubscriptionStatus() {
  const hasGo2 = !!data.go2StateListener.value;
  const hasImage = !!data.imageListener.value;
  console.log(`当前订阅状态 - GO2状态: ${hasGo2 ? '已订阅' : '未订阅'}, 相机图像: ${hasImage ? '已订阅' : '未订阅'}`);
  return { hasGo2, hasImage };
}

// 取消订阅函数
function unsubscribeAll() {
  try {
    const { hasGo2, hasImage } = checkSubscriptionStatus();
    
    if (!hasGo2 && !hasImage) {
      console.log('无需取消订阅，当前没有活跃订阅');
      return;
    }
    
    console.log('取消所有传感器数据订阅');
    
    // 取消GO2状态订阅
    if (data.go2StateListener.value) {
      data.go2StateListener.value.unsubscribe();
      data.go2StateListener.value = null;
      console.log('已取消GO2状态订阅');
    }
    
    // 取消相机图像订阅
    if (data.imageListener.value) {
      data.imageListener.value.unsubscribe();
      data.imageListener.value = null;
      console.log('已取消相机图像订阅');
    }
    
    // 清除传感器状态
    if (data.activeClientId.value && data.sensorStatus.value[data.activeClientId.value]) {
      data.sensorStatus.value[data.activeClientId.value] = {
        hasIMU: false,
        hasColor: false
      };
    }
    
    // 清除图像数据
    latestCameraImage.value = null;
    imuData.value = null;
    
    console.log('所有订阅已取消，状态已清理');
    
  } catch (error) {
    console.error("取消订阅失败:", error);
  }
}

    // 修改sampleSubscribe函数来更新实时数据
function sampleSubscribe() {
  try {
    // 检查ROS连接状态
    if (!data.isRosConnected.value || !data.ros.value) {
      console.log('ROS未连接，跳过订阅');
      return;
    }

    // 如果已有订阅，先检查是否需要重新订阅
    if (data.go2StateListener.value && data.imageListener.value) {
      console.log('传感器数据已订阅，跳过重复订阅');
      return;
    }
    
    // 如果已有部分订阅，先取消所有旧订阅
    if (data.go2StateListener.value || data.imageListener.value) {
      console.log('检测到部分订阅，先取消旧订阅');
      unsubscribeAll();
    }
    
    console.log('开始订阅传感器数据...');
    // 创建IMU订阅（go2）
    data.go2StateListener.value = new (ROSLIB as any).Topic({
      ros: data.ros.value,
      name: '/go2/state/sample',
      messageType: 'go2_msgs/msg/Go2State',
      qosProfile: {
        reliability: 'reliable',
        durability: 'volatile',
        history: 'keep_last',
        depth: 5
      }
    })

    data.go2StateListener.value.subscribe((msg: any) => {
      console.log('[GO2STATE]', msg)
      // 提取GO2STATE消息中的各个字段
      go2Position.value = msg.position || null
      go2Quaternion.value = msg.quaternion || null
      go2Rpy.value = msg.rpy || null
      go2Velocity.value = msg.velocity || null

      // 添加轨迹数据点
      if (isTrajectoryRecording.value && msg.position && typeof msg.position.x === 'number' && typeof msg.position.y === 'number') {
        const newPoint = {
          x: msg.position.x,
          y: msg.position.y,
          timestamp: Date.now()
        }

        trajectoryData.value.push(newPoint)
        console.log('添加轨迹点:', newPoint, '总点数:', trajectoryData.value.length)

        // 限制轨迹点数量，移除最旧的点
        if (trajectoryData.value.length > maxTrajectoryPoints.value) {
          trajectoryData.value.shift()
        }

        // 更新轨迹图表
        updateTrajectoryChart()
      } else {
        console.log('轨迹记录状态:', isTrajectoryRecording.value, '位置数据:', msg.position)
      }

      // 确保状态对象存在
      if (data.activeClientId.value && !data.sensorStatus.value[data.activeClientId.value]) {
        data.sensorStatus.value[data.activeClientId.value] = {
          hasIMU: false,
          hasColor: false
        };
      }
      // 检查IMU数据
      if (msg && msg.imu && msg.imu.orientation && msg.imu.linear_acceleration && msg.imu.angular_velocity) {
        if (data.activeClientId.value) {
          data.sensorStatus.value[data.activeClientId.value].hasIMU = true;
        }
        imuData.value = msg.imu;
      } else {
        if (data.activeClientId.value) {
          data.sensorStatus.value[data.activeClientId.value].hasIMU = false;
        }
      }
      addLog(msg)
    })

    // 相机订阅不变
    data.imageListener.value = new (ROSLIB as any).Topic({
      ros: data.ros.value,
      name: '/camera/camera/color/image_raw/compressed/sample',
      messageType: 'sensor_msgs/msg/CompressedImage'
    })
    data.imageListener.value.subscribe((msg: any) => {
      console.log('[COLOR]', msg)
      if (data.activeClientId.value && !data.sensorStatus.value[data.activeClientId.value]) {
        data.sensorStatus.value[data.activeClientId.value] = {
          hasIMU: false,
          hasColor: false
        };
      }
      if (msg && msg.data && msg.format && msg.format.includes('jpeg')) {
        if (data.activeClientId.value) {
          data.sensorStatus.value[data.activeClientId.value].hasColor = true;
        }
        latestCameraImage.value = `data:image/jpeg;base64,${msg.data}`;
        cameraInfo.value.format = msg.format;
        cameraInfo.value.frameCount++;
        const now = Date.now();
        const elapsed = now - cameraInfo.value.lastFrameTime;
        if (elapsed > 1000) {
          cameraInfo.value.fps = (cameraInfo.value.frameCount * 1000) / elapsed;
          cameraInfo.value.frameCount = 0;
          cameraInfo.value.lastFrameTime = now;
        }

        // 相机图像连接成功时，自动初始化轨迹图表
        if (logDisplayMode.value === 'dashboard' && !trajectoryChart) {
          nextTick(() => {
            initTrajectoryChart();
            console.log('相机图像连接成功，自动初始化轨迹图表');
          });
        }
      } else {
        if (data.activeClientId.value) {
          data.sensorStatus.value[data.activeClientId.value].hasColor = false;
        }
      }
      addLog(msg);
    })
  } catch (error) {
    console.error("订阅Topic失败:", error)
    ElMessage.error("订阅Topic失败")
  }
}

// 初始化图表
function initCharts() {
  // 初始化加速度图表
  const accelDom = document.getElementById('accelChart')
  if (accelDom) {
    accelChart = echarts.init(accelDom)
    const accelOption = {
      title: { text: '加速度值 (m/s²)' },
      tooltip: { trigger: 'axis' },
      legend: { data: ['X轴', 'Y轴', 'Z轴'] },
      grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },
      xAxis: { type: 'category', boundaryGap: false, data: [] },
      yAxis: { type: 'value' },
      series: [
        { name: 'X轴', type: 'line', data: [], lineStyle: { color: '#FF4500' } },
        { name: 'Y轴', type: 'line', data: [], lineStyle: { color: '#32CD32' } },
        { name: 'Z轴', type: 'line', data: [], lineStyle: { color: '#1E90FF' } }
      ]
    }
    accelChart.setOption(accelOption)
  }

  // 初始化陀螺仪图表
  const gyroDom = document.getElementById('gyroChart')
  if (gyroDom) {
    gyroChart = echarts.init(gyroDom)
    const gyroOption = {
      title: { text: '角速度 (rad/s)' },
      tooltip: { trigger: 'axis' },
      legend: { data: ['X轴', 'Y轴', 'Z轴'] },
      grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },
      xAxis: { type: 'category', boundaryGap: false, data: [] },
      yAxis: { type: 'value' },
      series: [
        { name: 'X轴', type: 'line', data: [], lineStyle: { color: '#FF4500' } },
        { name: 'Y轴', type: 'line', data: [], lineStyle: { color: '#32CD32' } },
        { name: 'Z轴', type: 'line', data: [], lineStyle: { color: '#1E90FF' } }
      ]
    }
    gyroChart.setOption(gyroOption)
  }

  // 初始化轨迹图表
  initTrajectoryChart()
}

// 初始化轨迹图表
function initTrajectoryChart() {
  console.log('正在初始化轨迹图表...')
  const trajectoryDom = document.getElementById('trajectoryChart')
  console.log('轨迹图表DOM元素:', trajectoryDom)

  if (trajectoryDom) {
    // 如果已存在图表实例，先销毁
    if (trajectoryChart) {
      trajectoryChart.dispose()
    }

    trajectoryChart = echarts.init(trajectoryDom)
    console.log('轨迹图表实例已创建:', trajectoryChart)

    const trajectoryOption = {
      backgroundColor: '#fff',
      title: {
        text: '机器狗2D移动轨迹',
        left: 'center',
        top: '5%',
        textStyle: {
          fontSize: 14,
          fontWeight: 'bold',
          color: '#303133'
        }
      },
      tooltip: {
        trigger: 'item',
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        borderColor: '#409EFF',
        borderWidth: 1,
        textStyle: {
          color: '#ffffff',
          fontSize: 12
        },
        formatter: function(params: any) {
          if (params.seriesName === '轨迹路径') {
            return `轨迹点<br/>X: ${params.data[0].toFixed(3)} 米<br/>Y: ${params.data[1].toFixed(3)} 米`
          } else if (params.seriesName === '当前位置') {
            return `当前位置<br/>X: ${params.data[0].toFixed(3)} 米<br/>Y: ${params.data[1].toFixed(3)} 米`
          }
          return ''
        }
      },
      grid: {
        left: '10%',
        right: '10%',
        bottom: '15%',
        top: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'value',
        name: 'X 坐标 (米)',
        nameLocation: 'middle',
        nameGap: 25,
        nameTextStyle: {
          color: '#606266',
          fontSize: 12
        },
        min: -2,
        max: 2,
        axisLine: {
          lineStyle: {
            color: '#dcdfe6'
          }
        },
        axisTick: {
          lineStyle: {
            color: '#dcdfe6'
          }
        },
        axisLabel: {
          color: '#606266',
          fontSize: 11
        },
        splitLine: {
          lineStyle: {
            color: '#ebeef5',
            type: 'dashed'
          }
        }
      },
      yAxis: {
        type: 'value',
        name: 'Y 坐标 (米)',
        nameLocation: 'middle',
        nameGap: 35,
        nameTextStyle: {
          color: '#606266',
          fontSize: 12
        },
        min: -2,
        max: 2,
        axisLine: {
          lineStyle: {
            color: '#dcdfe6'
          }
        },
        axisTick: {
          lineStyle: {
            color: '#dcdfe6'
          }
        },
        axisLabel: {
          color: '#606266',
          fontSize: 11
        },
        splitLine: {
          lineStyle: {
            color: '#ebeef5',
            type: 'dashed'
          }
        }
      },
      series: [
        {
          name: '轨迹路径',
          type: 'line',
          data: [],
          smooth: true,
          lineStyle: {
            width: 2,
            color: '#409EFF'
          },
          symbol: 'circle',
          symbolSize: 4,
          itemStyle: {
            color: '#409EFF',
            borderColor: '#fff',
            borderWidth: 1
          },
          markPoint: {
            data: [],
            symbol: 'pin',
            symbolSize: 25,
            itemStyle: {
              color: '#67c23a',
              borderColor: '#fff',
              borderWidth: 2
            },
            label: {
              show: true,
              formatter: '起点',
              position: 'top',
              color: '#67c23a',
              fontSize: 12,
              fontWeight: 'bold'
            }
          }
        },
        {
          name: '当前位置',
          type: 'scatter',
          data: [],
          symbol: 'circle',
          symbolSize: 12,
          itemStyle: {
            color: '#f56c6c',
            borderColor: '#fff',
            borderWidth: 2
          },
          label: {
            show: true,
            formatter: '当前位置',
            position: 'bottom',
            color: '#f56c6c',
            fontSize: 12,
            fontWeight: 'bold'
          }
        }
      ],
      // 添加全局动画配置
      animation: true,
      animationDuration: 1000,
      animationEasing: 'cubicOut',
      animationDelay: function (idx: number) {
        return idx * 50
      }
    }

    trajectoryChart.setOption(trajectoryOption)
    console.log('轨迹图表配置已设置')

    // 添加一些测试数据来验证图表是否正常工作
    setTimeout(() => {
      if (trajectoryChart && trajectoryData.value.length === 0) {
        console.log('添加测试轨迹数据')
        const testOption = {
          series: [
            {
              name: '轨迹路径',
              data: [[0, 0], [0.5, 0.3], [1, 0.8], [1.5, 0.5]]
            },
            {
              name: '当前位置',
              data: [[1.5, 0.5]]
            }
          ]
        }
        trajectoryChart.setOption(testOption)
      }
    }, 1000)
  } else {
    console.error('未找到轨迹图表DOM元素')
  }
}

// 更新轨迹图表
function updateTrajectoryChart() {
  if (!trajectoryChart) {
    console.log('轨迹图表实例不存在')
    return
  }

  if (trajectoryData.value.length === 0) {
    console.log('轨迹数据为空')
    return
  }

  console.log('更新轨迹图表，数据点数:', trajectoryData.value.length)

  const pathData = trajectoryData.value.map((point: any) => [point.x, point.y])
  const currentPosition = pathData[pathData.length - 1]
  const startPosition = pathData[0]

  // 计算数据范围用于自动缩放，保持一定的边距
  const xValues = pathData.map((point: any) => point[0])
  const yValues = pathData.map((point: any) => point[1])
  const xRange = Math.max(...xValues) - Math.min(...xValues)
  const yRange = Math.max(...yValues) - Math.min(...yValues)

  // 动态边距计算，确保最小显示范围
  const minRange = 0.5 // 最小显示范围（米）
  const actualRange = Math.max(xRange, yRange, minRange)
  const padding = actualRange * 0.3 // 30% 边距

  const xCenter = (Math.max(...xValues) + Math.min(...xValues)) / 2
  const yCenter = (Math.max(...yValues) + Math.min(...yValues)) / 2
  const halfRange = actualRange / 2 + padding

  const xMin = xCenter - halfRange
  const xMax = xCenter + halfRange
  const yMin = yCenter - halfRange
  const yMax = yCenter + halfRange

  // 删除未使用的trailData变量

  const option = {
    xAxis: {
      min: xMin,
      max: xMax
    },
    yAxis: {
      min: yMin,
      max: yMax
    },
    series: [
      {
        name: '轨迹路径',
        data: pathData,
        markPoint: {
          data: pathData.length > 0 ? [
            {
              coord: startPosition,
              name: '起点',
              itemStyle: {
                color: {
                  type: 'radial',
                  x: 0.5, y: 0.5, r: 0.5,
                  colorStops: [
                    { offset: 0, color: '#00ff88' },
                    { offset: 0.7, color: '#00cc66' },
                    { offset: 1, color: '#009944' }
                  ]
                },
                borderColor: '#ffffff',
                borderWidth: 2,
                shadowColor: '#00ff88',
                shadowBlur: 12
              }
            }
          ] : []
        }
      },
      {
        name: '当前位置',
        data: [currentPosition],
        // 添加呼吸动画效果
        animationDuration: 1500,
        animationEasing: 'sinusoidalInOut'
      }
    ]
  }

  trajectoryChart.setOption(option, false, true) // 不合并，立即更新
  console.log('轨迹图表已更新，当前位置:', currentPosition)
}

// 轨迹控制函数
function clearTrajectory() {
  console.log('清除轨迹数据')
  trajectoryData.value = []
  if (trajectoryChart) {
    const option = {
      series: [
        {
          name: '轨迹路径',
          data: [],
          markPoint: {
            data: []
          }
        },
        {
          name: '当前位置',
          data: []
        }
      ]
    }
    // 添加清除动画效果
    trajectoryChart.setOption(option, false, true)

    // 重置坐标轴范围
    setTimeout(() => {
      trajectoryChart.setOption({
        xAxis: { min: -2, max: 2 },
        yAxis: { min: -2, max: 2 }
      })
    }, 300)
  }
}

function toggleTrajectoryRecording() {
  isTrajectoryRecording.value = !isTrajectoryRecording.value
}

// 添加测试轨迹数据的函数
// function addTestTrajectoryData() {
//   console.log('添加测试轨迹数据')

//   // 创建一个更有趣的测试轨迹 - 螺旋形路径
//   const testPoints = []
//   const centerX = 0
//   const centerY = 0
//   const maxRadius = 1.5
//   const turns = 2
//   const pointCount = 20

//   for (let i = 0; i <= pointCount; i++) {
//     const progress = i / pointCount
//     const angle = progress * turns * 2 * Math.PI
//     const radius = progress * maxRadius

//     const x = centerX + radius * Math.cos(angle)
//     const y = centerY + radius * Math.sin(angle)

//     testPoints.push({
//       x: Number(x.toFixed(3)),
//       y: Number(y.toFixed(3)),
//       timestamp: Date.now() + i * 200
//     })
//   }

//   trajectoryData.value = testPoints
//   updateTrajectoryChart()
//   console.log('测试轨迹数据已添加，总点数:', trajectoryData.value.length)
// }

// 计算总移动距离
function calculateTotalDistance() {
  if (trajectoryData.value.length < 2) return 0

  let totalDistance = 0
  for (let i = 1; i < trajectoryData.value.length; i++) {
    const prev = trajectoryData.value[i - 1]
    const curr = trajectoryData.value[i]
    const dx = curr.x - prev.x
    const dy = curr.y - prev.y
    totalDistance += Math.sqrt(dx * dx + dy * dy)
  }

  return totalDistance
}

// 监听模式切换，初始化图表
watch(logDisplayMode, (newMode) => {
  if (newMode === 'dashboard') {
    // 延迟初始化以确保DOM已经渲染
    nextTick(() => {
      initCharts()
    })
  }
})

// 断开与ROS的连接
function disconnect(showNotification: boolean = false) {
  console.log('Disconnecting from ROS...');
  addLog("正在断开与ROS的连接...", 'info');
  
  // 断开 ROS 连接
  if (data.ros.value) {
    data.ros.value.close();
  }
  
  // 移除对Node.js process对象的引用
  addLog("ROS连接已断开", 'info');
  // ElMessage.info("ROS连接已断开"); // 已在滚动提示中显示
  
  // 只有在明确要求显示通知时才添加通知（比如用户主动断开时）
  if (showNotification) {
    addNotification('info', 'ROS 连接已关闭');
  }
}

const handleStopService = async (retryCount: number = 0) => {
  const stopRequest = new (ROSLIB as any).ServiceRequest({})
    // 停止 ROS 录制
    const stopService = new (ROSLIB as any).Service({
      ros: data.ros.value!,
      name: '/stop_recording',
      serviceType: 'std_srvs/Trigger'
    })

  stopService.callService(stopRequest, async (response: any) => {
    console.log('/stop_recording response:', response)
    const successMsg = `录制已停止: ${response.message || "成功"}`;
    addLog(successMsg, response.success ? 'success' : 'error');

    // 停止运行时长计时器
    stopDurationTimer();

    // 停止录制后，获取最新状态并检查是否真的停止了
    try {
      const statusResponse = await getRecordingStatus('END');
      
      // 检查是否真的停止了
      if (statusResponse.recording === true) {
        // 如果还在录制中，且重试次数小于2，则自动重试
        if (retryCount < 2) {
          addLog(`录制仍在进行中，正在进行第${retryCount + 1}次重试停止...`, 'info');
          console.log(`录制未停止，进行第${retryCount + 1}次重试`);
          // 延迟1秒后重试
          setTimeout(() => {
            handleStopService(retryCount + 1);
          }, 1000);
          return; // 不要结束loading，继续重试
        } else {
          // 已达到最大重试次数
          addLog(`已尝试${retryCount + 1}次停止，录制可能仍在进行中，请手动检查`, 'error');
          console.log(`已达到最大重试次数(${retryCount + 1})，停止失败`);
        }
      } else {
        // 成功停止，传感器数据继续实时显示
        addLog("录制已成功停止，传感器数据继续实时显示", 'success');
        
        // 确保传感器订阅状态正确，如果没有订阅则重新订阅
        if (!data.go2StateListener.value || !data.imageListener.value) {
          console.log('检测到传感器订阅缺失，正在重新建立订阅...');
          addLog("重新建立传感器数据订阅", 'info');
          sampleSubscribe();
        }
      }
    } catch (error) {
      console.error("获取停止后状态失败:", error);
      addLog("获取停止后状态失败，但停止操作已执行", 'info');
      
      // 即使状态查询失败，也要确保传感器订阅正常
      if (!data.go2StateListener.value || !data.imageListener.value) {
        console.log('状态查询失败但重新建立传感器订阅...');
        addLog("重新建立传感器数据订阅", 'info');
        sampleSubscribe();
      }
    }
    
    data.stopLoading.value = false; // 结束loading

    // 自定义需求
    if(data.collectionInfo.requirement === 39) {
      currentTaskId.value = data.customTaskId.value
    }

    // 移除自动创建采集结果的逻辑，改为在用户确认对话框中处理
  }, (error: Error) => {
    console.error('/stop_recording error:', error)
    const errorMsg = `停止录制失败: ${error.toString()}`;
    addLog(errorMsg, 'error');
    
    // 如果是网络或服务调用错误，且重试次数小于2，也可以重试
    if (retryCount < 2) {
      addLog(`停止服务调用失败，正在进行第${retryCount + 1}次重试...`, 'info');
      setTimeout(() => {
        handleStopService(retryCount + 1);
      }, 1000);
      return;
    }
    
    data.stopLoading.value = false; // 结束loading
  })
}

// 停止采集
const stopCollect = async () => {
  // 检查是否有选中的客户端
  if (!data.selectedClientId.value) {
    ElMessage.warning("请先选择一个客户端!");
    return;
  }
  
  // 检查当前活跃客户端
  if (!data.activeClientId.value) {
    ElMessage.warning("请先激活一个客户端!");
    return;
  }
  
  // 检查 ROS 连接状态
  if (!data.isRosConnected.value) {
    const errorMsg = "ROS 连接未建立，请等待连接...";
    ElMessage.error(errorMsg);
    addLog(errorMsg, 'error');
    return;
  }

  data.stopLoading.value = true; // 开始loading
  
  try {
    // 停止采集不需要取消传感器订阅，传感器数据应该继续实时显示
    addLog("正在停止录制服务，传感器数据将继续显示", 'info');
    
    // 延迟3秒后调用停止服务，让用户看到"停止中"状态
    addLog("正在停止采集服务...", 'info');
    setTimeout(() => {
      handleStopService();
    }, 3000);
  } catch (error) {
    console.error("停止任务失败:", error)
    const errorMsg = "停止失败: " + (error instanceof Error ? error.message : String(error));
    addLog(errorMsg, 'error');
    ElMessage.error(errorMsg);
    data.stopLoading.value = false; // 结束loading
  }
}



// 获取客户端日志
// const loadClientLogs = async (clientId: string | null = null) => {
//   try {
//     const response = await getClientLogsApi(clientId)
//     data.logs.value = response.data
    
//     // 确保日志按时间戳排序，最新的在前
//     data.logs.value.sort((a, b) => {
//       return (b.timestamp || 0) - (a.timestamp || 0)
//     })
    
//     // 如果启用了自动滚动
//     if (data.autoScroll.value) {
//       scrollLogsToTop()
//     }
//   } catch (error) {
//     console.error("获取日志失败:", error)
//   }
// }

// // 清除日志
// const clearLogs = async () => {
//   try {
//     // 确定是清除单个客户端的日志还是所有日志
//     const clientId = data.selectedClientId.value;

//     // 构建确认消息
//     const confirmMessage = clientId
//       ? `确定要清除客户端 ${clientId} 的所有日志吗?`
//       : `确定要清除所有客户端的日志吗?`;
    
//     await ElMessageBox.confirm(confirmMessage, '警告', {
//       confirmButtonText: '确定',
//       cancelButtonText: '取消',
//       type: 'warning'
//     });
    
//     // 显示加载中提示
//     const loadingInstance = ElMessage({
//       type: 'info',
//       message: '正在清除日志...',
//       duration: 0,
//       showClose: false
//     });
    
//     try {
//       // 在本地先清空日志显示，提高用户体验
//       data.logs.value = [];
      
//       // 调用API清除服务器端日志
//       const response = await clearClientLogsApi(clientId || '');
      
//       // 关闭加载提示
//       loadingInstance.close();
      
//       if (response && response.data && response.data.success) {
//         ElMessage.success(response.data.message || "日志已清除");
//         addLog("日志已清除", 'success');
//       } else {
//         const errorMsg = (response && response.data && response.data.message) || "清除日志失败";
//         ElMessage.error("清除日志失败: " + errorMsg);
//         addLog("清除日志失败: " + errorMsg, 'error');
        
//         // 如果清除失败，重新加载日志
//         await loadClientLogs(clientId || '');
//       }
//     } catch (apiError) {
//       // 关闭加载提示
//       loadingInstance.close();
      
//       console.error("API调用失败:", apiError);
//       const errorMsg = apiError instanceof Error ? apiError.message : String(apiError);
//       ElMessage.error("清除日志请求失败: " + errorMsg);
//       addLog("清除日志请求失败: " + errorMsg, 'error');
      
//       // 如果清除失败，重新加载日志
//       await loadClientLogs(clientId || '');
//     }
//   } catch (error) {
//     // 用户取消操作或其他错误
//     if (error !== 'cancel') {
//       console.error("清除日志操作失败:", error);
//       const errorMsg = error instanceof Error ? error.message : String(error);
//       ElMessage.error("操作失败: " + errorMsg);
//       addLog("清除日志操作失败: " + errorMsg, 'error');
//     }
//   }
// }

// 滚动日志到顶部
const scrollLogsToTop = () => {
  const logsContainer = document.getElementById("logsContainer")
  if (logsContainer) {
    logsContainer.scrollTop = 0
  }
}

// 添加自动重连相关变量
const autoReconnectEnabled = ref(true); // 默认启用自动重连
const reconnectAttempts = ref(0);
const maxReconnectAttempts = 5; // 最大重连尝试次数
const reconnectInterval = 3000; // 重连间隔时间（毫秒）
let reconnectTimer: any = null;

// 自动重连函数
const autoReconnect = () => {
  // 检查是否启用自动重连、是否有活跃客户端以及是否有选择的客户端
  if (!autoReconnectEnabled.value ||
      !data.activeClientId.value ||
      !data.selectedClientId.value) {
    reconnectAttempts.value = 0; // 重置重连计数
    return;
  }

  if (reconnectAttempts.value >= maxReconnectAttempts) {
    addLog(`已尝试重连 ${maxReconnectAttempts} 次但未成功，停止自动重连`, 'error');
    ElMessage.error(`自动重连失败，请手动刷新连接`);
    reconnectAttempts.value = 0;
    return;
  }

  reconnectAttempts.value++;
  addLog(`网络连接断开，正在尝试第 ${reconnectAttempts.value} 次自动重连...`, 'info');
  
  // 清除之前的定时器
  if (reconnectTimer) {
    clearTimeout(reconnectTimer);
  }
  
  // 设置重连定时器
  reconnectTimer = setTimeout(() => {
    // 再次检查是否仍有选中的客户端
    if (data.activeClientId.value && data.selectedClientId.value) {
      buildLink(data.activeClientId.value);
    } else {
      reconnectAttempts.value = 0; // 如果客户端已被清除，重置重连计数
    }
  }, reconnectInterval);
}

// 修改buildLink函数，连接成功后重置重连计数
function buildLink(ip:string) {
  console.log('%c [ ip ]-586', 'font-size:13px; background:#49c0fe; color:#8dffff;', ip)
  // 建立 ROS 连接
  data.ros.value = new (ROSLIB as any).Ros({
    url: `ws://${ip}`
  })

  // 确保ros.value不为null
  if (data.ros.value) {
    // 连接成功回调
    data.ros.value.on('connection', () => {
      console.log('Connected to ROS.');
      data.isRosConnected.value = true;
      data.rosError.value = null;
      // ElMessage.success("ROS 连接成功"); // 已在滚动提示中显示
      addLog("ROS 连接成功", 'success');
      // 移除之前的错误通知和连接中状态
      removeNotification('ROS 连接错误');
      removeNotification('ROS 连接已关闭');
      removeNotification('正在连接到客户端设备...');
      addNotification('success', 'ROS 连接成功');
      console.log('%c [ ros.value ]-591', 'font-size:13px; background:#49c0fe; color:#8dffff;', data.ros.value)

      // 连接成功后获取初始状态
      getRecordingStatus().catch(error => {
        console.error("获取初始状态失败:", error);
      });

      // 重置重连尝试计数
      reconnectAttempts.value = 0;

      // 连接成功后立即开始订阅传感器数据
      sampleSubscribe();
      console.log('ROS连接成功，开始订阅传感器数据');

      // 自动初始化轨迹图表
      nextTick(() => {
        if (logDisplayMode.value === 'dashboard') {
          initTrajectoryChart();
          console.log('ROS连接成功，自动初始化轨迹图表');
        }
      });
    });

    // 连接错误回调
    data.ros.value.on('error', (error?: Error) => {
      console.error('Error connecting to ROS:', error);
      data.isRosConnected.value = false;
      data.rosError.value = error?.toString() || '未知错误';
      // ElMessage.error("ROS 连接错误"); // 已在滚动提示中显示
      addLog("ROS 连接错误: " + (error?.toString() || '未知错误'), 'error');
      
      // 连接错误时取消所有订阅
      unsubscribeAll();
      
      // 移除连接中状态，添加错误通知
      removeNotification('正在连接到客户端设备...');
      addNotification('error', 'ROS 连接错误');
      
      // 如果是网络错误，尝试自动重连
      const errorString = error?.toString() || '';
      if (errorString.includes('WebSocket') || errorString.includes('network') || 
          errorString.includes('connect') || errorString.includes('timeout')) {
        autoReconnect();
      }
    });

    // 连接关闭回调
    data.ros.value.on('close', () => {
      console.log('Connection to ROS closed.');
      data.isRosConnected.value = false;
      data.rosError.value = null;
      // ElMessage.warning("ROS 连接已关闭"); // 已在滚动提示中显示
      addLog("ROS 连接已关闭", 'info');
      
      // 连接关闭时取消所有订阅
      unsubscribeAll();
      
      // 移除连接中状态，添加关闭通知
      removeNotification('正在连接到客户端设备...');
      addNotification('warning', 'ROS 连接已关闭');
      
      // 尝试自动重连
      autoReconnect();
    });
  }
}

// 添加一个函数来处理图像显示
function displayImageFromData(data: any): string | undefined {
  if (!data || typeof data !== 'object') return undefined;
  
  // 如果是压缩的图像数据
  if (data.format && data.format.includes('jpeg') && data.data) {
    // 提取Base64数据
    let base64Data = data.data;
    // 如果数据以/9j/开头，这是JPEG格式的标识
    if (base64Data.startsWith('/')) {
      return `data:image/jpeg;base64,${base64Data}`;
    }
  }
  return undefined;
}

// 添加一个处理直接显示Base64图像数据的函数
function displayRawImageData(jsonObj: any): string | undefined {
  try {
    // 判断是否为图像数据的JSON对象
    if (jsonObj && 
        jsonObj.header && 
        jsonObj.header.frame_id === "camera_color_optical_frame" && 
        jsonObj.format && 
        jsonObj.format.includes('jpeg') && 
        jsonObj.data) {
      
      // 获取Base64数据
      const imageData = jsonObj.data;
      if (typeof imageData === 'string' && imageData.startsWith('/')) {
        return `data:image/jpeg;base64,${imageData}`;
      }
    }
    return undefined;
  } catch (e) {
    console.error('处理图像数据时出错:', e);
    return undefined;
  }
}

// 添加一个获取状态的函数
function getRecordingStatus(status?: string): Promise<any> {
  return new Promise((resolve, reject) => {
    try {
      if (!data.isRosConnected.value || !data.ros.value) {
        const errorMsg = "ROS 连接未建立，请等待连接...";
        addLog(errorMsg, 'error');
        reject(errorMsg);
        return;
      }
      
      const getStatusService = new (ROSLIB as any).Service({
        ros: data.ros.value,
        name: '/get_status',
        serviceType: 'rosbag_service/GetStatus'
      });
      
      const statusRequest = new (ROSLIB as any).ServiceRequest({});
      
      getStatusService.callService(statusRequest, (response: any) => {
        console.log('/get_status response:', response);
        data.status_resp1.value = response;
        addLog("获取状态成功: " + JSON.stringify(response), 'success');
        
        // 记录上一次采集状态
        const prevStatus = data.collectStatus.value;
        // 根据响应中的recording字段更新采集状态
        if (response.recording === true) {
          data.collectStatus.value = '运行中';
          // 如果上一次不是运行中，说明刚刚开始，记录开始时间
          if (prevStatus !== '运行中') {
            data.collectStartTime.value = Date.now();
            data.collectEndTime.value = null;
            data.collectDuration.value = 0;
            console.log('采集开始，记录开始时间:', data.collectStartTime.value);
          }
        } else {
          // 如果上一次是运行中，现在变为停止，记录结束时间并计算持续时长
          if (prevStatus === '运行中') {
            data.collectEndTime.value = Date.now();
            if (data.collectStartTime.value) {
              data.collectDuration.value = Math.floor((data.collectEndTime.value - data.collectStartTime.value) / 1000);
            } else {
              data.collectDuration.value = 0;
            }
            console.log('采集停止，记录结束时间:', data.collectEndTime.value, '持续时长(秒):', data.collectDuration.value);
          }
          data.collectStatus.value = '停止';
          if (status === 'END') {
             verificationVisible.value = true
          }
        }

        if(response.verification_success) {
          data.verificationStatus.value = '成功';
        } else {
          data.verificationStatus.value = '暂无';
        }

        if(response.verification_message && response.verification_message ) {
          data.verificationMmessage.value = response.verification_message;
        } else {
          data.verificationMmessage.value = [];
        }
        
        resolve(response);
      }, (error: Error) => {
        console.error('/get_status error:', error);
        const errorMsg = `获取状态失败: ${error.toString()}`;
        data.collectStatus.value = '停止';
        data.startLoading.value = false
        data.stopLoading.value = false
        addLog(errorMsg, 'error');
        reject(error);
      });
    } catch (error) {
      const errorMsg = `获取状态发生异常: ${error instanceof Error ? error.message : String(error)}`;
      addLog(errorMsg, 'error');
      data.startLoading.value = false
      data.stopLoading.value = false
      reject(error);
    }
  });
}

// 添加传感器检测函数
async function checkSensors(): Promise<void> {
  return new Promise((resolve) => {
    if (!data.ros.value || !data.activeClientId.value) {
      resolve();
      return;
    }
    
    // 重置状态
    if (data.activeClientId.value) {
      data.sensorStatus.value[data.activeClientId.value] = {
        hasIMU: false,
        hasColor: false
      };
    }
    
    // 检测加速度传感器
    const accelTopic = new (ROSLIB as any).Topic({
      ros: data.ros.value,
      name: '/camera/camera/accel/sample/sample',
      messageType: 'sensor_msgs/msg/Imu'
    });
    
    // 检测陀螺仪传感器
    const gyroTopic = new (ROSLIB as any).Topic({
      ros: data.ros.value,
      name: '/camera/camera/gyro/sample/sample',
      messageType: 'sensor_msgs/msg/Imu'
    });
    
    // 检测相机传感器
    const colorTopic = new (ROSLIB as any).Topic({
      ros: data.ros.value,
      name: '/camera/camera/color/image_raw/compressed/sample',
      messageType: 'sensor_msgs/msg/CompressedImage'
    });
  
    // 设置2秒超时
    const timeoutId = setTimeout(() => {
      accelTopic.unsubscribe();
      gyroTopic.unsubscribe();
      colorTopic.unsubscribe();
      resolve();
    }, 2000);
    console.log('[timeoutId]', timeoutId)
    
    // 监听数据
    accelTopic.subscribe((msg: any) => {
      console.log('[ACCEL]', msg)
      data.sensorStatus.value[data.activeClientId.value!].hasIMU = true;
      accelTopic.unsubscribe();
    });
    
    gyroTopic.subscribe((msg: any) => {
      console.log('[GYRO]', msg)
      data.sensorStatus.value[data.activeClientId.value!].hasIMU = true;
      gyroTopic.unsubscribe();
    });
    
    colorTopic.subscribe((msg: any) => {
      console.log('[COLOR]', msg)
      data.sensorStatus.value[data.activeClientId.value!].hasColor = true;
      colorTopic.unsubscribe();
    });
  });
}

// 直接退出登录（用于logo点击）
const handleDirectLogout = async () => {
  console.log('handleDirectLogout called - direct logo click')
  
  try {
    console.log('Showing logout confirmation dialog')
    await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    console.log('User confirmed logout, proceeding...')
    
    // 清除本地存储
    localStorage.removeItem('collectorName')
    console.log('Local storage cleared')
    
    // 断开连接
    disconnect(true) // 用户主动退出，显示通知
    console.log('Disconnected from ROS')
    
    ElMessage.success('已退出登录')
    
    // 跳转到登录页
    console.log('Navigating to login page')
    router.push('/login')
  } catch (error) {
    console.log('User cancelled logout or error occurred:', error)
    // 用户取消操作
  }
}

// 退出登录（用于dropdown）
const handleLogout = async (command: string) => {
  console.log('handleLogout called with command:', command)
  
  if (command !== 'logout') {
    console.log('Command is not logout, returning')
    return
  }
  
  // 复用直接退出登录的逻辑
  await handleDirectLogout()
}

// 键盘事件处理
const handleKeydown = (event: KeyboardEvent) => {
  // ESC键处理
  if (event.key === 'Escape') {
    if (isFullscreenCamera.value && isFullscreenSidebarVisible.value) {
      // 如果在全屏模式下且侧边栏显示，优先关闭侧边栏
      isFullscreenSidebarVisible.value = false
    } else if (isFullscreenCamera.value) {
      // 否则退出全屏模式
      toggleFullscreenCamera()
    }
  }
}

// 页面加载时
onMounted(() => {
  // 检查登录状态
  const storedCollectorName = localStorage.getItem('collectorName')
  if (!storedCollectorName) {
    router.push('/login')
    return
  }
  data.collectorName.value = storedCollectorName
  
  // 添加测试日志
  addLog('系统已启动，等待客户端连接...', 'info')
  
  // 初始化传感器状态为未检测到
  data.clientOptions.value.forEach((client: any) => {
    if (client.value) {
      data.sensorStatus.value[client.value] = {
        hasIMU: false,
        hasColor: false
      }
    }
  })
  
  // 获取需求和指令列表
  fetchRequirements()
  fetchCommands()
  fetchMachines()
  
  // 添加窗口大小变化事件监听
  window.addEventListener('resize', () => {
    if (accelChart) accelChart.resize()
    if (gyroChart) gyroChart.resize()
    if (trajectoryChart) trajectoryChart.resize()
  })

  // 添加键盘事件监听
  window.addEventListener('keydown', handleKeydown)

  // 页面加载时自动初始化轨迹图表
  nextTick(() => {
    if (logDisplayMode.value === 'dashboard') {
      initTrajectoryChart();
      console.log('页面加载完成，自动初始化轨迹图表');
    }
  })
  
  // 通知轮播会在选择客户端时自动启动
})

// 页面卸载时
onUnmounted(() => {
  // 取消订阅
  if (data.go2StateListener.value) {
    data.go2StateListener.value.unsubscribe()
  }

  // 清理图表实例
  if (accelChart) {
    accelChart.dispose()
    accelChart = null
  }
  if (gyroChart) {
    gyroChart.dispose()
    gyroChart = null
  }
  if (trajectoryChart) {
    trajectoryChart.dispose()
    trajectoryChart = null
  }

  disconnect()
  // 断开 ROS 连接
  if (data.ros.value) {
    data.ros.value.close()
  }
  
  // 销毁图表实例
  if (accelChart) accelChart.dispose()
  if (gyroChart) gyroChart.dispose()
  
  // 移除窗口大小变化事件监听
  window.removeEventListener('resize', () => {})
  
  // 移除键盘事件监听
  window.removeEventListener('keydown', handleKeydown)
  
  // 清除重连定时器
  if (reconnectTimer) {
    clearTimeout(reconnectTimer);
  }

  // 清理运行时长计时器
  if (durationTimer) {
    clearInterval(durationTimer)
    durationTimer = null
  }
})

// 复制采集ID到剪贴板
const copyCollectionId = async () => {
  if (!uuid.value) return
  
  try {
    await navigator.clipboard.writeText(uuid.value)
    ElMessage.success('采集ID已复制到剪贴板')
  } catch (error) {
    // 如果navigator.clipboard不可用，使用传统方法
    try {
      const textArea = document.createElement('textarea')
      textArea.value = uuid.value
      textArea.style.position = 'fixed'
      textArea.style.opacity = '0'
      document.body.appendChild(textArea)
      textArea.select()
      document.execCommand('copy')
      document.body.removeChild(textArea)
      ElMessage.success('采集ID已复制到剪贴板')
    } catch (fallbackError) {
      console.error('复制失败:', fallbackError)
      ElMessage.error('复制失败，请手动复制')
    }
  }
}

// 更新传感器状态的辅助函数
// function updateSensorStatus(clientId: string | null, type: 'hasIMU' | 'hasColor', value: boolean) {
//   if (clientId && clientId in data.sensorStatus.value) {
//     data.sensorStatus.value[clientId][type] = value;
//   }
// }

// 根据需求ID获取相关指令选项
const fetchCommandsByRequirement = async (requirementId: string) => {
  try {
    commandsLoading.value = true
    console.log('根据需求ID获取指令选项:', requirementId)
    
    const res = await getTasksApi({ requirement_id: requirementId })
    
    if (Array.isArray(res.data)) {
      // 从任务列表中提取唯一的指令
      const commandIds = [...new Set(res.data.map((task: any) => task.command_id).filter(Boolean))]
      console.log('找到的指令IDs:', commandIds)
      
      // 根据指令ID匹配完整的指令信息
      const filteredCommands = commandList.value.filter((command: any) => 
        commandIds.includes(command.id)
      )
      
      // 更新指令选项
      data.instructionOptions.value = filteredCommands.map((item: any) => ({
        value: item.id,
        label: item.description
      }))
      
      // 检查当前选中的指令是否还在新的选项中，如果不在则清空
      if (data.collectionInfo.instruction && 
          !commandIds.includes(data.collectionInfo.instruction)) {
        console.log('当前选中的指令不在新的选项中，清空指令选择')
        data.collectionInfo.instruction = ''
      }
      
      console.log('更新后的指令选项:', data.instructionOptions.value)
    } else {
      console.warn('任务查询结果不是数组格式')
    }
  } catch (error: any) {
    console.error('根据需求获取指令失败:', error)
    ElMessage.error(`获取相关指令失败: ${error.message || '未知错误'}`)
  } finally {
    commandsLoading.value = false
  }
}

// 根据指令ID获取相关需求选项
const fetchRequirementsByCommand = async (commandId: string) => {
  try {
    requirementsLoading.value = true
    console.log('根据指令ID获取需求选项:', commandId)
    
    const res = await getTasksApi({ command_id: commandId })
    
    if (Array.isArray(res.data)) {
      // 从任务列表中提取唯一的需求
      const requirementIds = [...new Set(res.data.map((task: any) => task.requirement_id).filter(Boolean))]
      console.log('找到的需求IDs:', requirementIds)
      
      // 根据需求ID匹配完整的需求信息
      const filteredRequirements = requirementList.value.filter((requirement: any) => 
        requirementIds.includes(requirement.id)
      )
      
      // 更新需求选项
      data.requirementOptions.value = filteredRequirements.map((item: any) => ({
        value: item.id,
        label: item.title
      }))
      
      // 检查当前选中的需求是否还在新的选项中，如果不在则清空
      if (data.collectionInfo.requirement && 
          !requirementIds.includes(data.collectionInfo.requirement)) {
        console.log('当前选中的需求不在新的选项中，清空需求选择')
        data.collectionInfo.requirement = ''
      }
      
      console.log('更新后的需求选项:', data.requirementOptions.value)
    } else {
      console.warn('任务查询结果不是数组格式')
    }
  } catch (error: any) {
    console.error('根据指令获取需求失败:', error)
    ElMessage.error(`获取相关需求失败: ${error.message || '未知错误'}`)
  } finally {
    requirementsLoading.value = false
  }
}

// 需求变化处理函数
const onRequirementChange = async (requirementId: string) => {
  console.log('==== 需求变化处理开始 ====')
  console.log('传入的requirementId:', requirementId)
  if (requirementId && requirementId !== '' && requirementId !== null && requirementId !== undefined) {
    // 选中需求，筛指令
    await fetchCommandsByRequirement(requirementId)
  } else {
    // 清空需求，先拿全量需求，再清空指令，再拿全量指令
    await fetchRequirements()
    data.collectionInfo.instruction = ''
    await fetchCommands()
  }
  console.log('==== 需求变化处理结束 ====')
  if (data.collectionInfo.requirement && data.collectionInfo.instruction) {
    // 信息完整，查找task_id
    try {
      const taskRes = await getTasksApi({
        requirement_id: data.collectionInfo.requirement,
        command_id: data.collectionInfo.instruction
      })
      if (Array.isArray(taskRes.data) && taskRes.data.length > 0) {
        currentTaskId.value = taskRes.data[0].id
        addLog('已自动匹配任务ID: ' + currentTaskId.value, 'success')
      } else {
        currentTaskId.value = null
        addLog('未找到匹配的任务', 'error')
      }
    } catch (e: unknown) {
      currentTaskId.value = null
      const errorMessage = e instanceof Error ? e.message : String(e)
      addLog('查找任务ID失败: ' + errorMessage, 'error')
    }
  } else {
    currentTaskId.value = null
  }
}

// 指令变化处理函数
const onCommandChange = async (commandId: string) => {
  console.log('==== 指令变化处理开始 ====')
  console.log('传入的commandId:', commandId)
  console.log('commandId类型:', typeof commandId)
  console.log('当前指令选项数量:', data.instructionOptions.value.length)
  // dialogVisible.value = true
  if (commandId && commandId !== '' && commandId !== null && commandId !== undefined) {
    console.log('指令被选择，开始根据指令筛选需求')
    // 不再清空当前需求选择，只是根据选中的指令获取相关需求
    await fetchRequirementsByCommand(commandId)
  } else {
    // 如果清空指令，重新获取全量需求数据
    console.log('指令被清空，重新获取全量需求数据')
    await fetchRequirements()
    // 清空需求选择，因为用户主动清空了指令
    data.collectionInfo.requirement = ''
    console.log('需求选择已清空')
  }
  console.log('==== 指令变化处理结束 ====')
  if (data.collectionInfo.requirement && data.collectionInfo.instruction) {
    // 信息完整，查找task_id
    try {
      const taskRes = await getTasksApi({
        requirement_id: data.collectionInfo.requirement,
        command_id: data.collectionInfo.instruction
      })
      if (Array.isArray(taskRes.data) && taskRes.data.length > 0) {
        currentTaskId.value = taskRes.data[0].id
        addLog('已自动匹配任务ID: ' + currentTaskId.value, 'success')
      } else {
        currentTaskId.value = null
        addLog('未找到匹配的任务', 'error')
      }
    } catch (e: unknown) {
      currentTaskId.value = null
      const errorMessage = e instanceof Error ? e.message : String(e)
      addLog('查找任务ID失败: ' + errorMessage, 'error')
    }
  } else {
    currentTaskId.value = null
  }
}

// 新增 GO2STATE 数据用于实时展示
const go2Position = ref<any | null>(null)
const go2Quaternion = ref<any | null>(null)
const go2Rpy = ref<any | null>(null)
const go2Velocity = ref<any | null>(null)

// 在useHomeView外部添加
const currentTaskId = ref<string | null>(null)

// 创建采集结果记录的通用函数
const createDetailRecord = async (status: 'success' | 'cancel') => {
  // 自定义需求
  if(data.collectionInfo.requirement === 39) {
    currentTaskId.value = data.customTaskId.value
  }

  // 检查必要条件
  if (data.collectDuration.value > 0 && data.activeClientId.value && currentTaskId.value) {
    const detailPayload = {
      id: uuid.value,
      task_id: currentTaskId.value,
      executed_by_username: data.collectorName.value,
      status: status,
      time_consumption: data.collectDuration.value,
      machine_id: data.activeClientMachineId.value
    }

    try {
      await createDetailApi(detailPayload)
      const statusText = status === 'success' ? '成功' : '作废'
      addLog(`采集结果已记录为${statusText}状态`, 'success')
    } catch (e: unknown) {
      const errorMessage = e instanceof Error ? e.message : String(e)
      const statusText = status === 'success' ? '成功' : '作废'
      addLog(`记录采集结果失败(${statusText}): ${errorMessage}`, 'error')
      // 重新抛出错误，让调用方处理
      throw e
    }
  } else if (!currentTaskId.value) {
    const errorMsg = '未找到匹配的任务，无法记录采集结果'
    addLog(errorMsg, 'error')
    throw new Error(errorMsg)
  } else {
    const errorMsg = '采集条件不满足，无法记录采集结果'
    addLog(errorMsg, 'error')
    throw new Error(errorMsg)
  }
}

const handleErrorData = async () => {
  try {
    // 显示二次确认对话框
    await ElMessageBox.confirm(
      '该条采集记录将作废，请确认是否操作',
      '确认作废',
      {
        confirmButtonText: '确认作废',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: false
      }
    )

    // 用户确认后，执行作废操作
    try {
      await createDetailRecord('cancel')
      ElMessage.warning('采集记录已作废')
      verificationVisible.value = false
    } catch (error) {
      // 如果API调用失败，保持对话框打开，让用户可以重试
      console.error('作废操作失败:', error)
      ElMessage.error('作废操作失败，请重试')
    }
  } catch (error) {
    // 用户取消了二次确认，不执行任何操作，保持原对话框打开
    console.log('用户取消了作废操作')
  }
}
</script>

<script lang="ts">
export default {
  /** 控制面板 */
  name: 'HomeView'
}
</script>

<template>
  <div class="view">
    <div class="header-container">
      <div class="brand">
        <div class="logo-dropdown">
          <el-tooltip content="点击退出登录" placement="bottom" :show-after="500">
            <img src="@/assets/logo.png" alt="Logo" class="logo clickable-logo" @click="handleDirectLogout" />
          </el-tooltip>
        </div>
        <h1>数据采集系统 - 控制台</h1>
        
        <!-- 滚动提示区域 - 紧贴标题 -->
        <div class="notification-ticker">
          <div class="ticker-wrapper">
            <!-- 未选择客户端时显示静态提示 -->
            <div v-if="!data.selectedClientId.value" class="static-notification">
              <div class="ticker-indicator info"></div>
              <span class="ticker-text info">请选择客户端设备开始数据采集</span>
            </div>
            
            <!-- 已选择客户端且只有一个通知时显示静态通知 -->
            <div v-else-if="notifications.length === 1" class="static-notification">
              <div class="ticker-indicator" :class="notifications[0].type"></div>
              <span class="ticker-text" :class="notifications[0].type">{{ notifications[0].message }}</span>
            </div>
            
            <!-- 已选择客户端且有多个通知时显示滚动通知 -->
            <div v-else-if="notifications.length > 1" class="ticker-track" :style="{ transform: `translateY(${-notificationIndex * 24}px)` }">
              <div 
                v-for="(notification, index) in notifications" 
                :key="index"
                class="ticker-item"
              >
                <div class="ticker-indicator" :class="notification.type"></div>
                <span class="ticker-text" :class="notification.type">{{ notification.message }}</span>
              </div>
            </div>
            
            <!-- 已选择客户端但无通知时的默认提示 -->
            <div v-else class="static-notification">
              <div class="ticker-indicator success"></div>
              <span class="ticker-text success">系统运行正常，等待采集指令</span>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 用户信息区域 -->
      <div class="user-info">
        <el-dropdown @command="handleLogout" trigger="click">
          <div class="user-profile">
            <div class="user-avatar">
              <i class="avatar-icon"></i>
            </div>
            <div class="user-details">
              <span class="user-name">{{ data.collectorName.value }}</span>
              <span class="user-role">采集人</span>
            </div>
            <i class="dropdown-arrow"></i>
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="logout" class="logout-item">
                <i class="logout-icon"></i>
                退出登录
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
    
          <div class="content-wrapper">
        <!-- 全屏模式下的背景遮罩 -->
        <div v-if="isFullscreenCamera && isFullscreenSidebarVisible" class="fullscreen-overlay" @click="isFullscreenSidebarVisible = false"></div>
        
        <!-- 全屏模式下复用原有的侧边栏容器 -->
        <div v-if="isFullscreenCamera && isFullscreenSidebarVisible" class="fullscreen-sidebar-overlay" @click.stop>
          <div class="sidebar-container">
            <!-- 采集任务区域 -->
            <div class="sidebar-section task-section">
              <div class="section-header">
                <div class="section-title">采集任务</div>
              </div>

              <div class="section-content">
                <!-- 采集人信息 -->
                <div class="task-info-item">
                  <div class="info-label">采集人</div>
                  <div class="info-value">
                    <i class="user-icon"></i>
                    <span>{{ data.collectorName.value }}</span>
                  </div>
                </div>

                <!-- 采集ID信息 -->
                <div class="task-info-item collection-id-item" v-if="uuid">
                  <div class="info-label">采集ID</div>
                  <div class="info-value">
                    <el-tooltip 
                      :content="uuid" 
                      placement="top"
                      :disabled="false"
                    >
                      <span class="collection-id-text">{{ uuid }}</span>
                    </el-tooltip>
                    <button class="copy-btn" @click="copyCollectionId" title="复制采集ID">
                      <i class="copy-icon"></i>
                    </button>
                  </div>
                </div>

                <!-- 采集需求下拉框 -->
                <div class="task-select-item">
                  <el-select
                    v-model="data.collectionInfo.requirement"
                    placeholder="请选择采集需求"
                    class="task-select"
                    clearable
                    filterable
                    :loading="requirementsLoading"
                    :disabled="data.collectStatus.value === '运行中'"
                    @change="onRequirementChange"
                  >
                    <template #prefix>
                      <i class="task-icon requirement-icon"></i>
                    </template>
                    <el-option
                      v-for="option in data.requirementOptions.value"
                      :key="option.value"
                      :label="option.label"
                      :value="option.value"
                    />
                  </el-select>
                </div>

                <!-- 采集指令下拉框和自定义模式 -->
                <div class="task-select-item">
                  <!-- 自定义模式开关 -->
                  <div class="custom-mode-switch" v-if="data.collectionInfo.requirement && data.collectionInfo.requirement !== 39">
                    <span class="switch-label">自定义指令</span>
                    <el-switch
                      v-model="isCustomMode"
                      class="custom-switch"
                      :disabled="data.collectStatus.value === '运行中'"
                    />
                  </div>
                  
                  <!-- 下拉选择模式 -->
                  <el-select
                    v-if="data.collectionInfo.requirement !== 39 && !isCustomMode"
                    v-model="data.collectionInfo.instruction"
                    placeholder="请选择采集指令"
                    class="task-select"
                    clearable
                    filterable
                    :loading="commandsLoading"
                    :disabled="data.collectStatus.value === '运行中'"
                    @change="onCommandChange"
                  >
                    <template #prefix>
                      <i class="task-icon command-icon"></i>
                    </template>
                    <el-option
                      v-for="option in data.instructionOptions.value"
                      :key="option.value"
                      :label="option.label"
                      :value="option.value"
                    />
                  </el-select>
                  
                  <!-- 自定义输入模式 -->
                  <div v-if="data.collectionInfo.requirement !== 39 && isCustomMode" class="custom-instruction-container">
                    <el-input
                      v-model="customInstructionInput"
                      type="textarea"
                      :rows="3"
                      placeholder="请输入自定义指令描述"
                      class="custom-instruction-input"
                      :disabled="data.collectStatus.value === '运行中' || isCreatingCustomTask"
                    />
                    
                    <!-- 操作按钮 -->
                    <div class="custom-instruction-actions">
                      <button 
                        class="custom-action-btn cancel-btn"
                        @click="cancelCustomInstruction"
                        :disabled="data.collectStatus.value === '运行中' || isCreatingCustomTask"
                      >
                        取消
                      </button>
                      <button 
                        class="custom-action-btn confirm-btn"
                        @click="confirmCustomInstruction"
                        :disabled="data.collectStatus.value === '运行中' || isCreatingCustomTask || !customInstructionInput.trim()"
                      >
                        <span v-if="!isCreatingCustomTask">确认</span>
                        <span v-else>创建中...</span>
                      </button>
                    </div>
                  </div>
                  
                  <!-- 传统自定义需求的文本域 -->
                  <el-input
                    v-if="data.collectionInfo.requirement === 39"
                    type="textarea"
                    :rows="4"
                    v-model.trim="data.collectionInfo.instruction"
                    placeholder="请输入自定义指令"
                    class="task-select custom-textarea"
                    :disabled="data.collectStatus.value === '运行中'"
                    @blur="handleCustomInstructionBlur"
                  />
                </div>

                <!-- 信息完整性提示 -->
                <div class="task-status-indicator" v-if="data.collectionInfo.requirement && data.collectionInfo.instruction">
                  <i class="info-icon"></i>
                  <span class="status-text">已选择任务配置</span>
                </div>
              </div>
            </div>

            <!-- 任务控制区域 -->
            <div class="sidebar-section control-section">
              <div class="section-header">
                <div class="section-title">控制任务</div>
                <button class="refresh-button" @click="refreshConect" title="刷新连接" :disabled="isRefreshing">
                  <i v-if="!isRefreshing" class="refresh-icon"></i>
                  <i v-else class="refresh-loading"></i>
                </button>
              </div>

              <div class="section-content">
                <!-- 控制按钮组 -->
                <div class="control-buttons">
                  <button
                    class="control-btn start-btn"
                    @click="startCollect"
                    :disabled="!data.collectionInfo.requirement || !data.collectionInfo.instruction || !data.selectedClientId.value || data.collectStatus.value === '运行中' || data.startLoading.value"
                  >
                    <i class="btn-icon play-icon"></i>
                    <span>开始采集</span>
                  </button>
                  <button
                    class="control-btn stop-btn"
                    @click="stopCollect"
                    :disabled="!data.selectedClientId.value || data.collectStatus.value === '停止' || data.stopLoading.value"
                  >
                    <i v-if="!data.stopLoading.value" class="btn-icon stop-icon"></i>
                    <i v-else class="btn-icon loading-icon spinning"></i>
                    <span>{{ data.stopLoading.value ? '停止中...' : '停止采集' }}</span>
                  </button>
                </div>

                <!-- 状态显示区域 -->
                <div class="status-display">
                  <div class="status-item">
                    <span class="status-label">状态</span>
                    <span class="status-value" :class="data.collectStatus.value === '运行中' ? 'running' : 'stopped'">
                      {{ data.collectStatus.value }}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 客户端管理区域 -->
            <div class="sidebar-section client-section">
              <div class="section-header">
                <div class="section-title">客户端管理</div>
              </div>

              <div class="section-content">
                <!-- 客户端选择 -->
                <div class="task-select-item">
                  <el-select
                    v-model="data.selectedClientId.value"
                    placeholder="请选择客户端设备"
                    class="task-select"
                    @change="selectClient"
                    filterable
                    clearable
                    :loading="machinesLoading"
                  >
                    <template #prefix>
                      <i class="task-icon client-icon"></i>
                    </template>
                    <el-option
                      v-for="client in data.clientOptions.value"
                      :key="client.value+client.label"
                      :label="client.label"
                      :value="client.value"
                    />
                  </el-select>
                </div>

                <!-- 连接状态 -->
                <div class="connection-status" v-if="data.selectedClientId.value">
                  <div class="status-row">
                    <span class="status-label">连接状态</span>
                    <span class="status-value" :class="{ 'connected': data.isRosConnected.value }">
                      {{ data.isRosConnected.value ? '已连接' : '未连接' }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        

        
        <div class="main-container">
      <div class="left-column" :class="{ 
        'collapsed': isSidebarCollapsed && !isFullscreenCamera, 
        'fullscreen-hidden': isFullscreenCamera
      }" 
      @click.stop="() => {}"
      >
        <!-- 侧边栏折叠按钮 -->
        <div class="sidebar-toggle">
          <button class="toggle-btn" @click="toggleSidebar" :title="isSidebarCollapsed ? '展开侧边栏' : '收起侧边栏'">
            <i class="toggle-icon" :class="{ 'collapsed': isSidebarCollapsed }"></i>
          </button>
        </div>
        
        <!-- 折叠状态下的指示器 - 隐藏 -->
        <!-- <div v-if="isSidebarCollapsed" class="collapsed-indicator">
          <div class="indicator-dot" title="任务配置" @click="toggleSidebar">
            <i class="indicator-icon task-icon"></i>
          </div>
          <div class="indicator-dot" title="控制任务" @click="toggleSidebar">
            <i class="indicator-icon control-icon"></i>
          </div>
          <div class="indicator-dot" title="客户端管理" @click="toggleSidebar">
            <i class="indicator-icon client-icon"></i>
          </div>
        </div> -->
        
        <!-- 统一的侧边栏容器 -->
        <div class="sidebar-container" :class="{ 'collapsed': isSidebarCollapsed }" v-show="!isSidebarCollapsed && !isFullscreenCamera">
          <!-- 采集任务区域 -->
          <div class="sidebar-section task-section">
            <div class="section-header">
              <div class="section-title">采集任务</div>
            </div>

            <div class="section-content">
              <!-- 采集人信息 -->
              <div class="task-info-item">
                <div class="info-label">采集人</div>
                <div class="info-value">
                  <i class="user-icon"></i>
                  <span>{{ data.collectorName.value }}</span>
                </div>
              </div>

              <!-- 采集ID信息 -->
              <div class="task-info-item collection-id-item" v-if="uuid">
                <div class="info-label">采集ID</div>
                <div class="info-value">
                  <el-tooltip 
                    :content="uuid" 
                    placement="top"
                    :disabled="false"
                  >
                    <span class="collection-id-text">{{ uuid }}</span>
                  </el-tooltip>
                  <button class="copy-btn" @click="copyCollectionId" title="复制采集ID">
                    <i class="copy-icon"></i>
                  </button>
                </div>
              </div>

              <!-- 采集需求下拉框 -->
              <div class="task-select-item">
                <el-select
                  v-model="data.collectionInfo.requirement"
                  placeholder="请选择采集需求"
                  class="task-select"
                  clearable
                  filterable
                  :loading="requirementsLoading"
                  :disabled="data.collectStatus.value === '运行中'"
                  @change="onRequirementChange"
                >
                  <template #prefix>
                    <i class="task-icon requirement-icon"></i>
                  </template>
                  <el-option
                    v-for="option in data.requirementOptions.value"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </div>

              <!-- 采集指令下拉框和自定义模式 -->
              <div class="task-select-item">
                <!-- 自定义模式开关 -->
                <div class="custom-mode-switch" v-if="data.collectionInfo.requirement && data.collectionInfo.requirement !== 39">
                  <span class="switch-label">自定义指令</span>
                  <el-switch
                    v-model="isCustomMode"
                    class="custom-switch"
                    :disabled="data.collectStatus.value === '运行中'"
                  />
                </div>
                
                <!-- 下拉选择模式 -->
                <el-select
                  v-if="data.collectionInfo.requirement !== 39 && !isCustomMode"
                  v-model="data.collectionInfo.instruction"
                  placeholder="请选择采集指令"
                  class="task-select"
                  clearable
                  filterable
                  :loading="commandsLoading"
                  :disabled="data.collectStatus.value === '运行中'"
                  @change="onCommandChange"
                >
                  <template #prefix>
                    <i class="task-icon command-icon"></i>
                  </template>
                  <el-option
                    v-for="option in data.instructionOptions.value"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
                
                <!-- 自定义输入模式 -->
                <div v-if="data.collectionInfo.requirement !== 39 && isCustomMode" class="custom-instruction-container">
                  <el-input
                    v-model="customInstructionInput"
                    type="textarea"
                    :rows="3"
                    placeholder="请输入自定义指令描述"
                    class="custom-instruction-input"
                    :disabled="data.collectStatus.value === '运行中' || isCreatingCustomTask"
                  />
                  
                  <!-- 操作按钮 -->
                  <div class="custom-instruction-actions">
                    <button 
                      class="custom-action-btn cancel-btn"
                      @click="cancelCustomInstruction"
                      :disabled="data.collectStatus.value === '运行中' || isCreatingCustomTask"
                    >
                      取消
                    </button>
                    <button 
                      class="custom-action-btn confirm-btn"
                      @click="confirmCustomInstruction"
                      :disabled="data.collectStatus.value === '运行中' || isCreatingCustomTask || !customInstructionInput.trim()"
                    >
                      <span v-if="!isCreatingCustomTask">确认</span>
                      <span v-else>创建中...</span>
                    </button>
                  </div>
                </div>
                
                <!-- 传统自定义需求的文本域 -->
                <el-input
                  v-if="data.collectionInfo.requirement === 39"
                  type="textarea"
                  :rows="4"
                  v-model.trim="data.collectionInfo.instruction"
                  placeholder="请输入自定义指令"
                  class="task-select custom-textarea"
                  :disabled="data.collectStatus.value === '运行中'"
                  @blur="handleCustomInstructionBlur"
                />
              </div>

              <!-- 信息完整性提示 -->
              <div class="task-status-indicator" v-if="data.collectionInfo.requirement && data.collectionInfo.instruction">
                <i class="info-icon"></i>
                <span class="status-text">已选择任务配置</span>
              </div>
            </div>
          </div>

          <!-- 任务控制区域 -->
          <div class="sidebar-section control-section">
            <div class="section-header">
              <div class="section-title">控制任务</div>
              <button class="refresh-button" @click="refreshConect" title="刷新连接" :disabled="isRefreshing">
                <i v-if="!isRefreshing" class="refresh-icon"></i>
                <i v-else class="refresh-loading"></i>
              </button>
            </div>

            <div class="section-content">
              <!-- 控制按钮组 -->
              <div class="control-buttons">
                <button
                  class="control-btn start-btn"
                  @click="startCollect"
                  :disabled="!data.collectionInfo.requirement || !data.collectionInfo.instruction || !data.selectedClientId.value || data.collectStatus.value === '运行中' || data.startLoading.value"
                >
                  <i class="btn-icon play-icon"></i>
                  <span>开始采集</span>
                </button>
                <button
                  class="control-btn stop-btn"
                  @click="stopCollect"
                  :disabled="!data.selectedClientId.value || data.collectStatus.value === '停止' || data.stopLoading.value"
                >
                  <i v-if="!data.stopLoading.value" class="btn-icon stop-icon"></i>
                  <i v-else class="btn-icon loading-icon spinning"></i>
                  <span>{{ data.stopLoading.value ? '停止中...' : '停止采集' }}</span>
                </button>
              </div>

              <!-- 状态信息 -->
              <div class="status-info">
                <div class="status-row">
                  <span class="status-dot" :class="{'active': data.collectStatus.value === '运行中'}"></span>
                  <span class="status-label">当前状态:</span>
                  <span class="status-value">{{ data.collectStatus.value }}</span>
                  <span class="status-time">运行时长: {{ collectDuration }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 客户端管理区域 -->
          <div class="sidebar-section client-section">
            <div class="section-header">
              <div class="section-title">客户端管理</div>
              <div class="auto-reconnect-container">
                <span class="switch-label">自动重连</span>
                <el-switch
                  v-model="autoReconnectEnabled"
                  class="auto-reconnect-switch"
                  :title="autoReconnectEnabled ? '自动重连已启用' : '自动重连已禁用'"
                />
              </div>
            </div>

            <div class="section-content">
              <!-- 客户端选择器 -->
              <div class="client-select-item">
                <el-select
                  v-model="data.selectedClientId.value"
                  placeholder="请选择客户端设备"
                  class="client-select"
                  @change="selectClient"
                  filterable
                  clearable
                  :loading="machinesLoading"
                >
                  <template #prefix>
                    <i class="client-icon device-icon"></i>
                  </template>
                  <el-option
                    v-for="client in data.clientOptions.value"
                    :key="client.value+client.label"
                    :label="client.label"
                    :value="client.value"
                  />
                </el-select>
              </div>

              <!-- 重连状态指示器 -->
              <div v-if="reconnectAttempts > 0 && autoReconnectEnabled && data.selectedClientId.value" class="reconnect-status">
                <div class="reconnect-progress">
                  <div class="reconnect-bar" :style="{width: `${(reconnectAttempts / maxReconnectAttempts) * 100}%`}"></div>
                </div>
                <div class="reconnect-text">
                  <i class="reconnect-icon"></i>
                  正在尝试重连 ({{ reconnectAttempts }}/{{ maxReconnectAttempts }})
                </div>
              </div>

              <!-- 无客户端状态 -->
              <div v-if="!data.selectedClientId.value" class="no-clients">
                <div class="empty-client-state">
                  <i class="empty-icon"></i>
                  <span class="empty-text">请选择客户端设备</span>
                </div>
              </div>

              <!-- 已选择的客户端信息 -->
              <div v-else class="selected-client-container">
                <div class="client-info-card">
                  <div class="client-header">
                    <div class="client-details">
                      <div class="client-name">{{ getClientLabel(data.selectedClientId.value) }}</div>
                      <div class="client-status">
                        <span class="status-text">当前活跃</span>
                      </div>
                    </div>
                  </div>

                  <!-- 传感器状态面板 -->
                  <div class="sensors-panel">
                    <div class="sensors-title">传感器状态</div>
                    <div class="sensors-grid">
                      <div class="sensor-card" :class="{'sensor-active': data.activeClientId.value && data.sensorStatus.value[data.activeClientId.value]?.hasIMU}">
                        <div class="sensor-icon-container">
                          <i class="sensor-icon imu-icon"></i>
                        </div>
                        <div class="sensor-info">
                          <div class="sensor-name">IMU传感器</div>
                          <div class="sensor-status" :class="{'status-active': data.activeClientId.value && data.sensorStatus.value[data.activeClientId.value]?.hasIMU}">
                            {{ (data.activeClientId.value && data.sensorStatus.value[data.activeClientId.value]?.hasIMU) ? '在线' : '离线' }}
                          </div>
                        </div>
                      </div>
                      
                      <div class="sensor-card" :class="{'sensor-active': data.activeClientId.value && data.sensorStatus.value[data.activeClientId.value]?.hasColor}">
                        <div class="sensor-icon-container">
                          <i class="sensor-icon camera-icon"></i>
                        </div>
                        <div class="sensor-info">
                          <div class="sensor-name">RGB相机</div>
                          <div class="sensor-status" :class="{'status-active': data.activeClientId.value && data.sensorStatus.value[data.activeClientId.value]?.hasColor}">
                            {{ (data.activeClientId.value && data.sensorStatus.value[data.activeClientId.value]?.hasColor) ? '在线' : '离线' }}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="right-column">
        <!-- 日志卡片 -->
        <el-card class="card log-card">
          <template #header>
            <div class="card-header">
              <div class="log-mode-switch">
                <el-radio-group v-model="logDisplayMode">
                  <el-radio-button label="dashboard">数据监控</el-radio-button>
                  <el-radio-button label="record">采集记录</el-radio-button>
                  <el-radio-button label="log">日志记录</el-radio-button>
                </el-radio-group>
              </div>
            </div>
          </template>
          
          <div id="logsContainer" class="logs-container">
            
            <!-- 数据监控模式 -->
            <div v-if="logDisplayMode === 'dashboard'" class="dashboard-container">
              <!-- 相机面板 -->
              <div class="sensor-panel camera-panel" :class="{ 'fullscreen-camera': isFullscreenCamera }">
                  <!-- 全屏模式下的浮动箭头按钮 -->
                  <button 
                    v-if="isFullscreenCamera" 
                    class="fullscreen-arrow-btn"
                    @click="handleArrowClick"
                    :title="isFullscreenSidebarVisible ? '隐藏任务栏' : '显示任务栏'"
                  >
                    <i class="arrow-icon" :class="{ 'arrow-right': !isFullscreenSidebarVisible, 'arrow-left': isFullscreenSidebarVisible }"></i>
                  </button>
                  

                  <div class="sensor-header">
                    <span class="status-tag" :class="{'connected': data.activeClientId.value && data.sensorStatus.value[data.activeClientId.value]?.hasColor}">
                      {{ data.activeClientId.value && data.sensorStatus.value[data.activeClientId.value]?.hasColor ? '活跃' : '未连接' }}
                    </span>
                    <!-- 全屏切换眼睛图标 -->
                    <button 
                      class="fullscreen-eye-btn" 
                      @click="toggleFullscreenCamera"
                      :title="isFullscreenCamera ? '退出全屏模式' : '进入全屏模式'"
                    >
                      <i class="eye-icon" :class="{ 'eye-open': isFullscreenCamera, 'eye-closed': !isFullscreenCamera }"></i>
                    </button>
                  </div>
                  <div class="panel-content">
                    <div class="camera-container">
                      <div class="camera-status">
                        <div class="value-item">
                          <span class="label">帧率:</span>
                          <span class="value">{{ cameraInfo.fps.toFixed(1) }} FPS</span>
                        </div>
                        <div class="value-item">
                          <span class="label">格式:</span>
                          <span class="value">{{ cameraInfo.format || 'JPEG' }}</span>
                        </div>
                      </div>
                      <img v-if="latestCameraImage" :src="latestCameraImage" alt="实时相机图像" class="camera-image" />
                      <div v-else class="no-camera-feed">
                        <i class="camera-icon"></i>
                        <span>等待相机数据...</span>
                      </div>
                    </div>
                  </div>
                </div>

              <!-- 轨迹和IMU合并面板 -->
              <div v-show="!isFullscreenCamera" class="sensor-panel trajectory-imu-panel">
                <div class="trajectory-header">
                  <div class="trajectory-controls">
                    <span style="margin-right: 10px;" class="status-tag" :class="{'connected': isTrajectoryRecording}">
                      {{ isTrajectoryRecording ? '记录中' : '已暂停' }}
                    </span>
                    <el-button
                      class="trajectory-btn"
                      @click="toggleTrajectoryRecording"
                      :title="isTrajectoryRecording ? '暂停轨迹记录' : '恢复轨迹记录'"
                    >
                      {{ isTrajectoryRecording ? '暂停' : '恢复' }}
                    </el-button>
                    <el-button
                      class="trajectory-btn clear-btn"
                      @click="clearTrajectory"
                      title="清除轨迹"
                    >
                      清除
                    </el-button>
                    <el-button
                      class="trajectory-btn"
                      @click="initTrajectoryChart"
                      title="重新初始化图表"
                    >
                      初始化
                    </el-button>
                    <!-- <button
                      class="trajectory-btn"
                      @click="addTestTrajectoryData"
                      title="添加测试轨迹数据"
                    >
                      测试
                    </button> -->
                  </div>
                </div>
                <div class="panel-content trajectory-imu-content">
                  <!-- 左侧：移动轨迹 -->
                  <div class="trajectory-section">
                    <!-- <div class="section-title">移动轨迹</div> -->
                    <div id="trajectoryChart" class="trajectory-chart"></div>
                    <div class="trajectory-info">
                      <div class="value-item">
                        <span class="label">轨迹点数:</span>
                        <span class="value">{{ trajectoryData.length }}</span>
                      </div>
                      <div class="value-item">
                        <span class="label">当前位置:</span>
                        <span class="value">
                          {{ go2Position ? `(${go2Position.x?.toFixed(3)}, ${go2Position.y?.toFixed(3)})` : '(0.000, 0.000)' }}
                        </span>
                      </div>
                      <div class="value-item" v-if="trajectoryData.length > 1">
                        <span class="label">移动距离:</span>
                        <span class="value">
                          {{ calculateTotalDistance().toFixed(2) }} 米
                        </span>
                      </div>
                    </div>
                  </div>

                  <!-- 右侧：IMU数据 -->
                  <div class="imu-section">
                    <div class="section-title">
                      <!-- IMU数据 -->
                      <span class="status-tag" :class="{'connected': data.activeClientId.value && data.sensorStatus.value[data.activeClientId.value]?.hasIMU}">
                        {{ data.activeClientId.value && data.sensorStatus.value[data.activeClientId.value]?.hasIMU ? '已连接' : '未连接' }}
                      </span>
                    </div>
                    <div class="sensor-data">
                    <!-- GO2STATE数据展示 -->
                    <div class="data-section">
                      <h4>position</h4>
                      <div class="data-grid">
                        <div class="data-item">
                          <span class="label">X:</span>
                          <span class="value">{{ go2Position?.x?.toFixed(5) || '0.00000' }}</span>
                        </div>
                        <div class="data-item">
                          <span class="label">Y:</span>
                          <span class="value">{{ go2Position?.y?.toFixed(5) || '0.00000' }}</span>
                        </div>
                        <div class="data-item">
                          <span class="label">Z:</span>
                          <span class="value">{{ go2Position?.z?.toFixed(5) || '0.00000' }}</span>
                        </div>
                      </div>
                    </div>

                    <div class="data-section">
                      <h4>quaternion</h4>
                      <div class="data-grid">
                        <div class="data-item">
                          <span class="label">X:</span>
                          <span class="value">{{ go2Quaternion?.x?.toFixed(6) || '0.000000' }}</span>
                        </div>
                        <div class="data-item">
                          <span class="label">Y:</span>
                          <span class="value">{{ go2Quaternion?.y?.toFixed(6) || '0.000000' }}</span>
                        </div>
                        <div class="data-item">
                          <span class="label">Z:</span>
                          <span class="value">{{ go2Quaternion?.z?.toFixed(6) || '0.000000' }}</span>
                        </div>
                        <div class="data-item">
                          <span class="label">W:</span>
                          <span class="value">{{ go2Quaternion?.w?.toFixed(6) || '0.000000' }}</span>
                        </div>
                      </div>
                    </div>

                    <div class="data-section">
                      <h4>rpy</h4>
                      <div class="data-grid">
                        <div class="data-item">
                          <span class="label">X:</span>
                          <span class="value">{{ go2Rpy?.x?.toFixed(6) || '0.000000' }}</span>
                        </div>
                        <div class="data-item">
                          <span class="label">Y:</span>
                          <span class="value">{{ go2Rpy?.y?.toFixed(6) || '0.000000' }}</span>
                        </div>
                        <div class="data-item">
                          <span class="label">Z:</span>
                          <span class="value">{{ go2Rpy?.z?.toFixed(6) || '0.000000' }}</span>
                        </div>
                      </div>
                    </div>

                    <div class="data-section">
                      <h4>velocity</h4>
                      <div class="data-grid">
                        <div class="data-item">
                          <span class="label">X:</span>
                          <span class="value">{{ go2Velocity?.x?.toFixed(6) || '0.000000' }}</span>
                        </div>
                        <div class="data-item">
                          <span class="label">Y:</span>
                          <span class="value">{{ go2Velocity?.y?.toFixed(6) || '0.000000' }}</span>
                        </div>
                        <div class="data-item">
                          <span class="label">Z:</span>
                          <span class="value">{{ go2Velocity?.z?.toFixed(6) || '0.000000' }}</span>
                        </div>
                      </div>
                    </div>
                    <!-- 其余IMU数据展示保持不变 -->
                    <div class="data-section">
                      <h4>linear_acceleration</h4>
                      <div class="data-grid">
                        <div class="data-item">
                          <span class="label">X:</span>
                          <span class="value">{{ imuData?.linear_acceleration?.x?.toFixed(4) || '0' }} m/s²</span>
                        </div>
                        <div class="data-item">
                          <span class="label">Y:</span>
                          <span class="value">{{ imuData?.linear_acceleration?.y?.toFixed(4) || '0' }} m/s²</span>
                        </div>
                        <div class="data-item">
                          <span class="label">Z:</span>
                          <span class="value">{{ imuData?.linear_acceleration?.z?.toFixed(4) || '0' }} m/s²</span>
                        </div>
                      </div>
                    </div>
                    <div class="data-section">
                      <h4>angular_velocity</h4>
                      <div class="data-grid">
                        <div class="data-item">
                          <span class="label">X:</span>
                          <span class="value">{{ imuData?.angular_velocity?.x?.toFixed(4) || '0' }} rad/s</span>
                        </div>
                        <div class="data-item">
                          <span class="label">Y:</span>
                          <span class="value">{{ imuData?.angular_velocity?.y?.toFixed(4) || '0' }} rad/s</span>
                        </div>
                        <div class="data-item">
                          <span class="label">Z:</span>
                          <span class="value">{{ imuData?.angular_velocity?.z?.toFixed(4) || '0' }} rad/s</span>
                        </div>
                      </div>
                    </div>
                    <div class="data-section">
                      <h4>orientation</h4>
                      <div class="data-grid">
                        <div class="data-item">
                          <span class="label">X:</span>
                          <span class="value">{{ imuData?.orientation?.x?.toFixed(4) || '0' }}</span>
                        </div>
                        <div class="data-item">
                          <span class="label">Y:</span>
                          <span class="value">{{ imuData?.orientation?.y?.toFixed(4) || '0' }}</span>
                        </div>
                        <div class="data-item">
                          <span class="label">Z:</span>
                          <span class="value">{{ imuData?.orientation?.z?.toFixed(4) || '0' }}</span>
                        </div>
                        <div class="data-item">
                          <span class="label">W:</span>
                          <span class="value">{{ imuData?.orientation?.w?.toFixed(4) || '0' }}</span>
                        </div>
                      </div>
                    </div>
                    <div class="data-section">
                      <h4>covariance</h4>
                      <div class="covariance-grid">
                        <div class="covariance-item">
                          <span class="label">orientation_covariance:</span>
                          <span class="value">{{ imuData?.orientation_covariance?.join(', ') }}</span>
                        </div>
                        <div class="covariance-item">
                          <span class="label">angular_velocity_covariance:</span>
                          <span class="value">{{ imuData?.angular_velocity_covariance?.join(', ') }}</span>
                        </div>
                        <div class="covariance-item">
                          <span class="label">linear_acceleration_covariance:</span>
                          <span class="value">{{ imuData?.linear_acceleration_covariance?.join(', ') }}</span>
                        </div>
                      </div>
                    </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 日志记录模式 -->
            <div v-if="logDisplayMode === 'log'" class="logs-list">
              <div v-if="data.logs.value.length === 0" class="log-empty">
                <div class="empty-state-container">
                  <div class="empty-state-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="120" height="120">
                      <path fill="#e0e6ed" d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8l-6-6z"/>
                      <path fill="#cad4e0" d="M14 2v6h6"/>
                      <path fill="#8096b0" d="M16 13H8v-1h8v1zm0 3H8v-1h8v1zm-4 3H8v-1h4v1z"/>
                      <circle cx="12" cy="9" r="1" fill="#8096b0"/>
                    </svg>
                  </div>
                  <div class="empty-state-text">
                    <h3>暂无日志记录</h3>
                    <p>系统未收到任何日志记录，请选择客户端并开始采集数据后查看</p>
                  </div>
                </div>
              </div>
              
              <div v-for="(log, index) in data.logs.value" :key="index" 
                   class="log-item" 
                   :class="{ 
                     'log-info': log.log_level === 'info', 
                     'log-success': log.log_level === 'success', 
                     'log-error': log.log_level === 'error' 
                   }">
                <!-- 如果是所有客户端的日志，则显示客户端名称 -->
                <div v-if="!data.selectedClientId.value && log.client_name" class="log-client">
                  {{ log.client_name }}
                </div>
                
                <div class="log-time">
                  {{ log.formatted_time || formatTimestamp(log.timestamp) }}
                </div>
                
                <!-- 根据日志类型显示不同内容 -->
                <!-- 图像类型 -->
                <div v-if="log.type === 'image'" class="log-image-container">
                  <img :src="`data:image/jpeg;base64,${log.message}`" alt="Image data" class="log-image" />
                  
                  <div v-if="log.metadata" class="log-metadata">
                    尺寸: {{ log.metadata.width }}x{{ log.metadata.height }} | 
                    格式: {{ log.metadata.format || 'jpeg' }}
                  </div>
                  
                  <div class="log-image-actions">
                    <el-button 
                      size="small" 
                      @click="downloadImage(log.message, log.formatted_time)"
                    >
                      下载
                    </el-button>
                  </div>
                </div>
                
                <!-- TurboJPEG图像类型 -->
                <div v-else-if="log.type === 'turbo_image'" class="log-binary">
                  <div>收到TurboJPEG编码图像 (需要服务器端解码)</div>
                  
                  <div v-if="log.metadata" class="log-metadata">
                    尺寸: {{ log.metadata.width }}x{{ log.metadata.height }} | 
                    格式: TurboJPEG
                  </div>
                  <div v-else class="log-metadata">TurboJPEG编码图像</div>
                  
                  <div class="log-image-actions">
                    <el-button 
                      size="small" 
                      @click="downloadBinary(log.message, `turbo_image_${log.formatted_time.replace(/[: ]/g, '_')}.jpg`)"
                    >
                      下载原始数据
                    </el-button>
                  </div>
                </div>
                
                <!-- 二进制类型 -->
                <div v-else-if="log.type === 'binary'" class="log-binary">
                  <div>
                    收到二进制文件: {{ log.metadata?.filename || 'unnamed_file' }}
                    ({{ log.metadata?.filesize ? formatBytes(log.metadata.filesize) : '未知大小' }})
                  </div>
                  
                  <div class="log-binary-actions">
                    <el-button 
                      size="small" 
                      @click="downloadBinary(log.message, log.metadata?.filename || 'file.bin')"
                    >
                      下载文件
                    </el-button>
                  </div>
                </div>
                
                <!-- JSON或文本类型 -->
                <div v-else class="log-message">
                  <!-- JSON格式化显示 -->
                  <div v-if="log.type === 'json'" style="margin: 0; overflow: auto;">
                    <!-- 可折叠对象的标题行 -->
                    <div class="json-summary" @click="log.expanded = !log.expanded" style="cursor: pointer; user-select: none;">
                      <span>{{ log.expanded ? '▼' : '▶' }} {{ log.originalMessage && log.originalMessage.header ? log.originalMessage.header.frame_id : '' }}</span>
                      <span class="json-type">{{ log.originalMessage ? data.getJsonSummary(log.originalMessage) : '' }}</span>
                    </div>
                    
                    <!-- 如果是图像数据，首先尝试显示图像 -->
                    <div v-if="log.originalMessage && log.originalMessage.format && log.originalMessage.format.includes('jpeg')" class="image-preview">
                      <img v-if="displayImageFromData(log.originalMessage)" :src="displayImageFromData(log.originalMessage)" alt="Image data" class="log-image" />
                      <div v-else class="no-image">无法显示图像数据</div>
                    </div>
                    
                    <!-- 适用于JSON camera_color_optical_frame对象的图像显示 -->
                    <div v-else-if="log.originalMessage && log.originalMessage.header && log.originalMessage.header.frame_id === 'camera_color_optical_frame'" class="image-preview">
                      <img v-if="displayRawImageData(log.originalMessage)" :src="displayRawImageData(log.originalMessage)" alt="Image data" class="log-image" />
                      <div v-else class="no-image">无法显示图像数据</div>
                      
                      <div class="image-actions">
                        <el-button 
                          size="small" 
                          type="primary"
                          @click="downloadImage(log.originalMessage.data, `camera_image_${Date.now()}.jpg`)"
                          :disabled="!log.originalMessage.data"
                        >
                          下载图片
                        </el-button>
                        <el-button 
                          size="small" 
                          @click="log.expanded = !log.expanded"
                        >
                          {{ log.expanded ? '隐藏详情' : '显示详情' }}
                        </el-button>
                      </div>
                    </div>
                    
                    <!-- 折叠/展开内容 -->
                    <pre v-if="log.expanded">{{ log.message }}</pre>
                  </div>
                  
                  <!-- 普通文本显示 -->
                  <div v-else>{{ log.message || '未知消息内容' }}</div>
                </div>
                
                <!-- 任务ID显示 -->
                <div v-if="log.task_id" class="log-task-id">
                  任务ID: {{ log.task_id }}
                </div>
              </div>
            </div>

            <DetailManage v-if="logDisplayMode === 'record'" :isInHomePage="true" />
          </div>
        </el-card>
      </div>
    </div>
    
    <footer class="footer">
      <div class="footer-content">
        <span class="copyright">© 2025 Lion Rock Data Collection System. All Rights Reserved.</span>
        <span class="version">Version 1.0.0</span>
      </div>
    </footer>
    </div>
  </div>
  <CommandDialog
    :visible="dialogVisible"
    title="采集指令"
    @confirm="handleConfirm"
    @cancel="handleCancel"
    @update:visible="(val: boolean) => dialogVisible = val"
  >
    <!-- <div>这里是自定义内容</div> -->
  </CommandDialog>

  <el-dialog v-model="verificationVisible" title="提示" width="330px" :close-on-click-modal="false" :close-on-press-escape="false">
    <!-- <div style="text-align:center;font-size:18px;">采集已停止</div> -->
    <div class="info-status">
        <div class="status-indicator" :class="{'complete': data.verificationStatus.value === '成功'}">
          <span class="status-dot" :class="{'active':  data.verificationStatus.value === '成功'}"></span>
          <span class="status-text">
            {{   data.verificationStatus.value === '成功' ? '数据校验成功' : '数据校验不通过' }}
          </span>
        </div>
      </div>
      <div v-if="Array.isArray(data.verificationMmessage.value) && data.verificationMmessage.value.length > 0" class="error-messages">
          <div v-for="(msg, idx) in data.verificationMmessage.value" :key="idx" class="error-item">
            <span class="error-icon">×</span>
            <span>{{ msg }}</span>
          </div>
        </div>
    <template #footer>
      <el-button type="primary" v-if="data.verificationStatus.value === '成功'" @click="handleConfirm">确认</el-button>
      <el-button type="danger" @click="handleErrorData">作废</el-button>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>
.view {
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  max-width: 100%;
  margin: 0 auto;
  box-sizing: border-box;
  color: #2c3e50;
  min-height: 100vh;
  overflow-x: hidden; /* 防止水平滚动条 */
  display: flex;
  flex-direction: column; /* 使footer能够固定在底部 */
}

.header-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background-color: #fff;
  padding: 16px 24px;
  border-bottom: 1px solid #eaeaea;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 通知滚动条样式 */
.notification-ticker {
  margin-left: 24px;
  width: 300px;
  height: 24px;
  overflow: hidden;
  position: relative;
}

.ticker-wrapper {
  width: 100%;
  height: 100%;
  position: relative;
}

.ticker-track {
  transition: transform 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: transform;
}

.ticker-item {
  display: flex;
  align-items: center;
  gap: 8px;
  height: 24px;
  line-height: 24px;
}

.ticker-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  flex-shrink: 0;
  
  &.info {
    background: #909399;
    box-shadow: 0 0 8px rgba(144, 147, 153, 0.6);
  }
  
  &.error {
    background: #F56C6C;
    box-shadow: 0 0 8px rgba(245, 108, 108, 0.6);
  }
  
  &.warning {
    background: #E6A23C;
    box-shadow: 0 0 8px rgba(230, 162, 60, 0.6);
  }
  
  &.success {
    background: #67C23A;
    box-shadow: 0 0 8px rgba(103, 194, 58, 0.6);
  }
}

.ticker-text {
  font-size: 16px;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  
  &.info {
    color: #909399;
  }
  
  &.error {
    color: #F56C6C;
  }
  
  &.warning {
    color: #E6A23C;
  }
  
  &.success {
    color: #67C23A;
  }
}

.static-notification {
  display: flex;
  align-items: center;
  gap: 8px;
  height: 24px;
  line-height: 24px;
}

/* 移除不需要的动画 */

.brand {
  display: flex;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
}

.user-profile {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 16px;
  border-radius: 12px;
  background-color: #f5f7fa;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid #ebeef5;
  
  &:hover {
    background-color: #ecf5ff;
    border-color: #b3d8ff;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
}

.user-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: linear-gradient(135deg, #409EFF 0%, #3a8ee6 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
}

.avatar-icon {
  display: inline-block;
  width: 20px;
  height: 20px;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/></svg>');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.user-details {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 2px;
}

.user-name {
  font-size: 15px;
  font-weight: 600;
  color: #303133;
  line-height: 1.2;
}

.user-role {
  font-size: 12px;
  color: #909399;
  line-height: 1.2;
}

.dropdown-arrow {
  display: inline-block;
  width: 12px;
  height: 12px;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23909399"><path d="M7 10l5 5 5-5z"/></svg>');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  transition: transform 0.3s ease;
}

.user-profile:hover .dropdown-arrow {
  transform: rotate(180deg);
}

.logout-item {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #f56c6c;
  
  &:hover {
    background-color: #fef0f0;
    color: #f56c6c;
  }
}

.logout-icon {
  display: inline-block;
  width: 14px;
  height: 14px;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23f56c6c"><path d="M17 7l-1.41 1.41L18.17 11H8v2h10.17l-2.58 2.59L17 17l5-5zM4 5h8V3H4c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h8v-2H4V5z"/></svg>');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

/* Logo下拉框样式 */
.logo-dropdown {
  margin-right: 16px;
  
  .logo {
    height: 36px;
    transition: all 0.3s ease;
    
    &.clickable-logo {
      cursor: pointer;
      border-radius: 8px;
      padding: 4px;
      
      &:hover {
        transform: scale(1.1) rotate(5deg);
        box-shadow: 0 4px 12px rgba(220, 38, 127, 0.3);
        background: rgba(220, 38, 127, 0.1);
      }
      
      &:active {
        transform: scale(0.95) rotate(-2deg);
      }
    }
  }
}

h1 {
  margin: 0;
  color: #3a5169;
  text-align: left;
  font-size: 24px;
  font-weight: 600;
  letter-spacing: 0.5px;
  background: linear-gradient(120deg, #2c3e50, #4a6285);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0px 1px 2px rgba(0, 0, 0, 0.1);
  font-family: 'PingFang SC', 'Microsoft YaHei', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

.content-wrapper {
  margin-top: 86px;/*固定的header留出空间 */
  height: calc(100vh - 84px); /* 设置高度为视口高度减去header高度 */
  overflow: hidden; /* 隐藏最外层滚动条 */
}

.main-container {
  padding: 16px 20px 20px;
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
  overflow-y: auto; /* 允许主容器滚动 */
  overflow-x: hidden; /* 防止水平滚动 */
  flex: 1; /* 让主内容区域占用剩余空间 */
  height: calc(100vh - 96px); /* 减少高度损失 */
  
  /* 主容器滚动条样式 */
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: rgba(235, 238, 245, 0.1);
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: #9093994d;
    border-radius: 3px;
    transition: all 0.3s ease;
    
    &:hover {
      background: rgba(144, 147, 153, 0.5);
    }
    
    &:active {
      background: rgba(144, 147, 153, 0.7);
    }
  }
}

.left-column {
  flex: 0 0 380px;
  display: flex;
  flex-direction: column;
  height: fit-content; /* 让左侧栏高度适应内容 */
  position: relative;
  transition: all 0.3s ease;

  &.collapsed {
    flex: 0 0 0px;
  }
}

/* 侧边栏折叠按钮样式 */
.sidebar-toggle {
  position: absolute;
  top: 10px;
  right: -15px;
  z-index: 1000;
}

.left-column.collapsed .sidebar-toggle {
  position: fixed;
  left: 10px;
  top: 120px;
  right: auto;
}

.toggle-btn {
  position: relative;
  left: -6px;
  width: 34px;
  height: 34px;
  background: transparent;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;

  &:hover {
    transform: scale(1.2);
  }

  &:active {
    transform: scale(0.9);
  }
}

.toggle-icon {
  width: 24px;
  height: 24px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23606266'%3E%3Cpath d='M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  transition: all 0.3s ease;

  &.collapsed {
    transform: rotate(180deg);
  }
}

.toggle-btn:hover .toggle-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23409EFF'%3E%3Cpath d='M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z'/%3E%3C/svg%3E");
}

/* 折叠状态指示器样式 */
.collapsed-indicator {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 12px 2px;
  align-items: center;
}

.indicator-dot {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    background: #409EFF;
    border-color: #409EFF;
    transform: scale(1.1);
    
    .indicator-icon {
      filter: brightness(0) invert(1);
    }
  }
}

.indicator-icon {
  width: 10px;
  height: 10px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  
  &.task-icon {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%236c757d'%3E%3Cpath d='M9 11H7v6h2v-6zm4 0h-2v6h2v-6zm4 0h-2v6h2v-6zm2-7h-3V2h-2v2H8V2H6v2H3c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 16H3V8h14v12z'/%3E%3C/svg%3E");
  }
  
  &.control-icon {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%236c757d'%3E%3Cpath d='M8 5v14l11-7z'/%3E%3C/svg%3E");
  }
  
  &.client-icon {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%236c757d'%3E%3Cpath d='M20 18c1.1 0 1.99-.9 1.99-2L22 6c0-1.1-.9-2-2-2H4c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2H0v2h24v-2h-4zM4 6h16v10H4V6z'/%3E%3C/svg%3E");
  }
}

/* 新的侧边栏容器样式 */
.sidebar-container {
  background-color: #fff;
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #e8eaed;
  transition: all 0.3s ease;

  &.collapsed {
    opacity: 0;
    transform: translateX(-100%);
    pointer-events: none;
  }
}



.sidebar-section {
  border-bottom: 1px solid #f0f2f5;

  &:last-child {
    border-bottom: none;
  }
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px 8px 20px;
  .section-title {
    font-family: Roboto;
    font-size: 18px;
    font-weight: 500;
    line-height: 24px;
    letter-spacing: normal;
    color: #000000;
  }
}



.section-content {
  padding: 8px 20px 20px;
}

/* 采集任务区域样式 */
.info-item {
  margin-bottom: 12px;

  &:last-child {
    margin-bottom: 0;
  }
}

.info-label {
  width: 80px;
  display: block;
  font-size: 12px;
  font-weight: 500;
  color: #6b7280;
  margin-bottom: 6px;
  line-height: 1.4;
}

.collector-display {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px 16px;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 4px;
  border: 1px solid #cbd5e1;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;

  &:hover {
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
    border-color: #94a3b8;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
  }
}

.collector-avatar {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 6px rgba(59, 130, 246, 0.4);
  border: 2px solid white;
}

.collector-icon {
  width: 12px;
  height: 12px;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/></svg>');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.collector-name {
  font-size: 14px;
  font-weight: 600;
  color: #1e293b;
  letter-spacing: 0.3px;
}

.info-select {
  width: 100%;

  :deep(.el-input__inner) {
    font-size: 13px;
    height: 38px;
    line-height: 38px;
    border-color: #d1d5db;
    border-radius: 4px;
    transition: all 0.3s ease;

    &:focus {
      border-color: #3b82f6;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    &:hover {
      border-color: #9ca3af;
    }
  }

  :deep(.el-select__caret) {
    color: #6b7280;
  }

  /* 修复下拉框阴影重叠问题 */
  :deep(.el-select-dropdown) {
    z-index: 2000 !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
    border: 1px solid #e5e7eb !important;
    border-radius: 4px !important;
    margin-top: 4px !important;
  }

  :deep(.el-select-dropdown__item) {
    padding: 8px 12px;
    font-size: 13px;

    &:hover {
      background-color: #f3f4f6;
    }

    &.selected {
      background-color: #eff6ff;
      color: #3b82f6;
      font-weight: 500;
    }
  }
}

.info-status {
  margin-top: 8px;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px 16px;
  // background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  border: 1px solid #f59e0b;
  border-radius: 20px;
  box-shadow: 0 2px 4px rgba(245, 158, 11, 0.2);
  transition: all 0.3s ease;

  &.complete {
    // background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
    border-color: #10b981;
    box-shadow: 0 2px 4px rgba(16, 185, 129, 0.2);

    .status-dot {
      background-color: #10b981;
    }

    .status-text {
      color: #065f46;
      font-weight: 600;
    }
  }

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(245, 158, 11, 0.3);

    &.complete {
      box-shadow: 0 4px 8px rgba(16, 185, 129, 0.3);
    }
  }
}

.status-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #f59e0b;
  box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.2);
  transition: all 0.3s ease;

  &.active {
    background-color: #10b981;
    box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2), 0 0 8px rgba(16, 185, 129, 0.4);
    animation: pulse-glow 2s infinite;
  }
}

.status-text {
  font-size: 13px;
  font-weight: 600;
  color: #92400e;
  letter-spacing: 0.2px;
}

.right-column {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
  transition: all 0.3s ease;
  height: calc(100vh - 96px);
}

@media (max-width: 1200px) {
  .main-container {
    // flex-direction: column;
    height: auto; /* 在小屏幕上允许高度自适应 */
  }

  .left-column,
  .right-column {
    width: 100%;
    min-width: auto;
    height: auto;
  }

  .logs-container {
    max-height: 600px;
  }
}

@media (max-width: 768px) {
  .header-container {
    padding: 12px 16px;
  }
  
  .content-wrapper {
    margin-top: 76px; /* 在移动设备上调整上边距 */
    height: calc(100vh - 76px);
  }
  
  .main-container {
    padding: 16px;
    height: calc(100vh - 92px);
  }
  
  .brand h1 {
    font-size: 20px;
  }
  
  .logo-dropdown .logo.clickable-logo {
    height: 32px;
    padding: 3px;
  }
  
  .user-profile {
    padding: 6px 12px;
    gap: 8px;
  }
  
  .user-name {
    font-size: 14px;
  }
  
  .user-avatar {
    width: 28px;
    height: 28px;
  }
}

/* 保留日志卡片的样式 */
.card {
  background-color: #fff;
  border-radius: 4px;
  padding: 0;
  margin-bottom: 24px;
  box-shadow: 0 3px 15px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  border: none;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
  }

  &.log-card {
    padding: 0;
    margin-bottom: 8px;
    height: calc(100vh - 120px);
    display: flex;
    flex-direction: column;
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  h2 {
    margin: 0;
    font-size: 18px;
    font-weight: 500;
    color: #3a5169;
    letter-spacing: 0.5px;
  }
}



.refresh-button {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  margin-bottom: 20px;
  border-radius: 6px;
  background-color: #f3f4f6;
  border: 1px solid #e5e7eb;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;

  &:hover {
    background-color: #3b82f6;
    border-color: #3b82f6;

    .refresh-icon {
      background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M17.65 6.35A7.958 7.958 0 0 0 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08A5.99 5.99 0 0 1 12 18c-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"/></svg>');
    }
  }

  &:active {
    .refresh-icon {
      animation: rotate 0.8s linear;
    }
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

.refresh-loading {
  width: 14px;
  height: 14px;
  border: 2px solid #e5e7eb;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.refresh-icon {
  width: 14px;
  height: 14px;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%236b7280"><path d="M17.65 6.35A7.958 7.958 0 0 0 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08A5.99 5.99 0 0 1 12 18c-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"/></svg>');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  transition: all 0.2s ease;
}

.button-group {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
}

.action-button {
  flex: 1;
  height: 40px;
  border: none;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  &:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: 20px;
    background-repeat: no-repeat;
    background-position: 24px center;
    opacity: 0.15;
  }
}

.button-content {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 2;
}

.button-icon {
  display: inline-block;
  width: 18px;
  height: 18px;
  margin-right: 8px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

/* 添加 loading 图标样式 */
:deep(.el-icon-loading) {
  width: 18px;
  height: 18px;
  margin-right: 8px;
  animation: rotating 2s linear infinite;
}

@keyframes rotating {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.start-button {
  background-color: #3b82f6;
  color: white;

  &:hover:not(:disabled) {
    background-color: #2563eb;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
  }

  &:before {
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M8 5v14l11-7z"/></svg>');
    background-size: 60px;
    opacity: 0.1;
  }
}

.start-icon {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M8 5v14l11-7z"/></svg>');
}

.stop-button {
  background-color: #ef4444;
  color: white;

  &:hover:not(:disabled) {
    background-color: #dc2626;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(239, 68, 68, 0.3);
  }

  &:before {
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M6 6h12v12H6z"/></svg>');
    background-size: 60px;
    opacity: 0.1;
  }
}

.stop-icon {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M6 6h12v12H6z"/></svg>');
}

.status-monitor {
  margin: 16px 0 12px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.status-title {
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 8px;
  font-weight: 500;
}

.status-monitor .status-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 20px;
  padding: 12px 20px;
  min-width: 140px;
  border: 1px solid #cbd5e1;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;

  &.status-running {
    background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
    border-color: #16a34a;
    box-shadow: 0 3px 8px rgba(22, 163, 74, 0.25);

    .status-text {
      color: #15803d;
      font-weight: 600;
    }
  }

  &.status-stopped {
    background: linear-gradient(135deg, #fef2f2 0%, #fecaca 100%);
    border-color: #dc2626;
    box-shadow: 0 3px 8px rgba(220, 38, 38, 0.25);

    .status-text {
      color: #dc2626;
      font-weight: 600;
    }
  }

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
}

.status-monitor .status-dot {
  display: inline-block;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #ef4444;
  box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2);
  transition: all 0.3s ease;

  &.active {
    background-color: #10b981;
    box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2), 0 0 10px rgba(16, 185, 129, 0.5);
    animation: pulse-glow 2s infinite;
  }
}

.status-monitor .status-text {
  font-weight: 600;
  color: #6b7280;
  font-size: 14px;
  letter-spacing: 0.3px;
}

.status-active {
  color: #10b981;
}

.status-inactive {
  color: #ef4444;
}

/* 运行时长显示样式 */
.duration-display {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  border-radius: 12px;
  padding: 12px 16px;
  color: white;
  box-shadow: 0 3px 10px rgba(79, 70, 229, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(79, 70, 229, 0.4);
  }
}

.duration-label {
  font-size: 13px;
  font-weight: 500;
  opacity: 0.9;
  letter-spacing: 0.3px;
}

.duration-time {
  font-size: 18px;
  font-weight: 700;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Courier New', monospace;
  letter-spacing: 1.2px;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  background: linear-gradient(45deg, #ffffff, #e0e7ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

@keyframes pulse {
  0% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.7);
  }

  70% {
    transform: scale(1);
    box-shadow: 0 0 0 4px rgba(16, 185, 129, 0);
  }

  100% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(16, 185, 129, 0);
  }
}

@keyframes pulse-glow {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2), 0 0 8px rgba(16, 185, 129, 0.4);
  }
  50% {
    transform: scale(1.1);
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.3), 0 0 12px rgba(16, 185, 129, 0.6);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2), 0 0 8px rgba(16, 185, 129, 0.4);
  }
}

.task-id-container {
  margin-top: 12px;
  background-color: #f3f4f6;
  padding: 8px 12px;
  border-radius: 6px;
  text-align: center;
  font-size: 12px;

  strong {
    color: #374151;
  }

  span {
    color: #6b7280;
  }
}

.recording-status {
  margin-top: 12px;
  font-size: 12px;
  color: #ef4444;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.recording-indicator {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #ef4444;
  animation: blink 1s infinite;
}

@keyframes blink {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.3;
  }
  100% {
    opacity: 1;
  }
}

.client-list {
  list-style-type: none;
  padding: 0;
  margin: 0;
  max-height: calc(100vh - 240px);
  overflow-y: auto;
}

.no-clients {
  padding: 20px;
  text-align: center;
  color: #909399;
  font-style: italic;
}

.client-item {
  padding: 16px;
  background-color: #f9fafc;
  margin-bottom: 12px;
  border-radius: 10px;
  display: flex;
  flex-direction: column;
  gap: 12px;
  border: 1px solid #ebeef5;
  transition: all 0.3s ease;
  
  &:hover {
    border-color: #c0c4cc;
    transform: translateY(-2px);
  }
}

.client-info {
  display: flex;
  flex-direction: column;
  gap: 6px;
  
  strong {
    font-size: 16px;
    color: #303133;
  }
}

.client-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 6px;
}

.client-status-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin: 6px 0;
}

.client-status {
  padding: 5px 12px;
  border-radius: 20px;
  font-size: 13px;
  font-weight: 600;
  
  &.client-active {
    background-color: #67c23a;
    color: white;
  }
  
  &.client-inactive {
    background-color: #f56c6c;
    color: white;
  }
  
  &.status-collecting {
    background-color: #67c23a;
    color: white;
  }
  
  &.status-stopped {
    background-color: #909399;
    color: white;
  }
}

.timestamp {
  font-size: 12px;
  color: #909399;
  margin-top: 6px;
}

.task-id {
  font-size: 13px;
  color: #606266;
  margin-top: 6px;
  display: block;
}

.log-controls {
  display: flex;
  align-items: center;
  gap: 16px;
}

.clear-logs-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  background-color: #f5f7fa;
  color: #606266;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s;
  
  &:hover {
    background-color: #ebeef5;
    color: #409EFF;
  }
  
  &:active {
    background-color: #e6e8eb;
  }
}

.clear-icon {
  display: inline-block;
  width: 14px;
  height: 14px;
  margin-right: 6px;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23606266"><path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"/></svg>');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.logs-container {
  flex: 1 1 auto;
  min-height: 100px; /* 减小最小高度 */
  max-height: calc(100vh - 180px); /* 增加可用高度 */
  overflow-y: auto;
  border-radius: 4px;
  padding: 12px; /* 减小内边距 */
  
  /* 自定义滚动条样式 */
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: rgba(235, 238, 245, 0.3);
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: rgba(144, 147, 153, 0.4);
    border-radius: 3px;
    transition: background 0.3s ease;
    
    &:hover {
      background: rgba(144, 147, 153, 0.6);
    }
  }
}

.logs-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.log-empty {
  text-align: center;
  color: #909399;
  padding: 30px;
  font-size: 15px;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

.empty-state-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  max-width: 400px;
}

.empty-state-icon {
  margin-bottom: 20px;
  opacity: 0.8;
}

.empty-state-text {
  text-align: center;
}

.empty-state-text h3 {
  font-size: 18px;
  color: #606266;
  margin-bottom: 8px;
  font-weight: 500;
}

.empty-state-text p {
  color: #909399;
  font-size: 14px;
  line-height: 1.6;
  margin: 0;
}

.log-item {
  padding: 16px;
  background-color: white;
  border-left: 3px solid #67c23a;
  border-radius: 8px;
  transition: all 0.2s ease;
  
  &:hover {
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
  }
  
  &.log-info {
    border-left-color: #909399;
  }
  
  &.log-success {
    border-left-color: #67c23a;
  }
  
  &.log-error {
    border-left-color: #f56c6c;
  }
}

.log-client {
  font-weight: bold;
  color: #409EFF;
  margin-bottom: 4px;
}

.log-time {
  font-size: 13px;
  color: #909399;
  margin-bottom: 6px;
  border-bottom: 1px solid #f0f2f5;
  padding-bottom: 6px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.log-message {
  word-wrap: break-word;
  line-height: 1.5;
  padding: 3px 0;
  font-size: 14px;
}

.log-task-id {
  font-size: 13px;
  color: #606266;
  margin-top: 6px;
  font-style: italic;
}

.log-type {
  display: inline-block;
  font-size: 12px;
  padding: 3px 8px;
  border-radius: 12px;
  color: white;
  background-color: #909399;
  font-weight: 500;
  
  &.log-type-text {
    background-color: #607D8B;
  }
  
  &.log-type-json {
    background-color: #409EFF;
  }
  
  &.log-type-image {
    background-color: #67c23a;
  }
  
  &.log-type-binary {
    background-color: #E6A23C;
  }
  
  &.log-type-turbo_image {
    background-color: #9C27B0;
  }
}

.log-image-container {
  position: relative;
}

.log-image {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.log-image-actions,
.log-binary-actions {
  margin-top: 10px;
}

.log-metadata {
  font-size: 13px;
  color: #606266;
  margin-top: 6px;
  background-color: #f5f7fa;
  padding: 6px 10px;
  border-radius: 6px;
  display: inline-block;
}

.log-binary {
  color: #606266;
  font-size: 14px;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 6px;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
}

.json-summary {
  background: #f5f7fa;
  padding: 10px;
  border-radius: 6px;
  margin-bottom: 6px;
  display: flex;
  justify-content: space-between;
  cursor: pointer;
}

.json-type {
  color: #909399;
  font-size: 0.9em;
}

.image-preview {
  margin: 12px 0;
  text-align: center;
  background: #f8f8f8;
  padding: 16px;
  border-radius: 10px;
  box-shadow: 0 2px 6px rgba(0,0,0,0.05);
}

.log-image {
  max-width: 100%;
  max-height: 400px;
  border-radius: 6px;
  border: 1px solid #ebeef5;
}

.no-image {
  padding: 16px;
  background-color: #f5f7fa;
  border-radius: 8px;
  color: #909399;
  text-align: center;
}

.image-actions {
  margin-top: 12px;
  display: flex;
  justify-content: center;
  gap: 12px;
}

:deep(.el-select .el-input .el-input__wrapper) {
  border-radius: 8px;
  padding: 4px 15px;
}

:deep(.el-button) {
  border-radius: 8px;
  font-weight: 500;
}

:deep(.el-checkbox__label) {
  font-size: 14px;
}

:deep(.el-card__header) {
  padding: 16px 24px;
  // background-color: #f5f7fa;
}

:deep(.el-card__body) {
  padding: 3px;
}

.footer {
  padding: 10px 24px;
  // background-color: #f5f7fa;
  // border-top: 1px solid #eaeaea;
  margin-top: auto; /* 推到底部 */
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1400px;
  margin: 0 auto;
  font-size: 12px;
  color: #909399;
  font-family: 'Arial', sans-serif;
}

.copyright {
  letter-spacing: 0.5px;
}

.version {
  font-style: italic;
}

.selected-clients-list {
  margin-top: 10px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.selected-client-item {
  padding: 12px;
  border-radius: 6px;
  background-color: #f9fafc;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid #ebeef5;
  
  &:hover {
    background-color: #f0f2f5;
    transform: translateY(-2px);
  }
  
  &.active-client {
    background-color: #ecf5ff;
    border-color: #b3d8ff;
  }
}

.client-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.client-status {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 10px;
}

.client-active {
  background-color: #67c23a;
  color: white;
}

.client-inactive {
  background-color: #909399;
  color: white;
}

/* 客户端管理样式 */
.client-section {
  .section-header {
    .auto-reconnect-container {
      display: flex;
      align-items: center;
      gap: 8px;
      
      .switch-label {
        font-size: 13px;
        color: #6c757d;
        font-weight: 500;
      }
      
      .auto-reconnect-switch {
        :deep(.el-switch__core) {
          height: 20px;
          min-width: 40px;
        }
        
        :deep(.el-switch__action) {
          width: 16px;
          height: 16px;
        }
      }
    }
  }
}

.client-select-item {
  margin-bottom: 16px;
}

.client-select {
  width: 100%;

  :deep(.el-input__wrapper) {
    border: 1px solid #cacfd1d8;
    border-radius: 8px;
    padding: 12px 16px;
    box-shadow: none;
    transition: all 0.3s ease;

    &:hover {
      border-color: #ced4da;
      background-color: #f1f3f4;
    }

    &.is-focus {
      border-color: #4B8BF4;
      background-color: #ffffff;
      box-shadow: 0 0 0 3px rgba(75, 139, 244, 0.1);
    }
  }

  :deep(.el-input__inner) {
    color: #495057;
    font-size: 15px;
    font-weight: 500;

    &::placeholder {
      color: #6c757d;
      font-weight: normal;
    }
  }

  :deep(.el-input__prefix) {
    display: flex;
    align-items: center;
    margin-right: 8px;
  }

  :deep(.el-select__caret) {
    color: #6c757d;
    font-size: 14px;
  }

  :deep(.el-select-dropdown) {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    margin-top: 4px;
  }
}

.client-icon {
  width: 16px;
  height: 16px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;

  &.device-icon {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%236c757d'%3E%3Cpath d='M4 6h16v2H4zm0 5h16v2H4zm0 5h16v2H4z M2 4v16c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2H4c-1.1 0-2 .9-2 2z'/%3E%3C/svg%3E");
  }
}

.reconnect-status {
  margin-bottom: 16px;
  padding: 12px;
  background-color: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 8px;

  .reconnect-progress {
    width: 100%;
    height: 4px;
    background-color: #f1c40f;
    border-radius: 2px;
    overflow: hidden;
    margin-bottom: 8px;

    .reconnect-bar {
      height: 100%;
      background: linear-gradient(90deg, #f39c12, #e67e22);
      transition: width 0.3s ease;
    }
  }

  .reconnect-text {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 13px;
    color: #856404;
    font-weight: 500;

    .reconnect-icon {
      width: 14px;
      height: 14px;
      background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23856404'%3E%3Cpath d='M12 6v3l4-4-4-4v3c-4.42 0-8 3.58-8 8 0 1.57.46 3.03 1.24 4.26L6.7 14.8c-.45-.83-.7-1.79-.7-2.8 0-3.31 2.69-6 6-6zm6.76 1.74L17.3 9.2c.44.84.7 1.79.7 2.8 0 3.31-2.69 6-6 6v-3l-4 4 4 4v-3c4.42 0 8-3.58 8-8 0-1.57-.46-3.03-1.24-4.26z'/%3E%3C/svg%3E");
      background-size: contain;
      background-repeat: no-repeat;
      animation: rotate 1.5s linear infinite;
    }
  }
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.no-clients {
  margin-top: 20px;
}

.empty-client-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 32px 16px;
  text-align: center;
  
  .empty-icon {
    width: 64px;
    height: 64px;
    margin-bottom: 16px;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23cbd5e1'%3E%3Cpath d='M4 6h16v2H4zm0 5h16v2H4zm0 5h16v2H4z M2 4v16c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2H4c-1.1 0-2 .9-2 2z'/%3E%3C/svg%3E");
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    opacity: 0.5;
  }
  
  .empty-text {
    font-size: 14px;
    color: #6b7280;
    font-weight: 500;
  }
}

.selected-client-container {
  margin-top: 8px;
}

.client-info-card {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border: 1px solid #cbd5e1;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
  }
}

.client-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px dashed #cbd5e1;

  .client-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #4B8BF4 0%, #3b7ae4 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 3px 10px rgba(75, 139, 244, 0.3);

    .client-device-icon {
      width: 20px;
      height: 20px;
      background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M20 18c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2H4c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2H0v2h24v-2h-4zM4 6h16v10H4V6z'/%3E%3C/svg%3E");
      background-size: contain;
      background-repeat: no-repeat;
    }
  }

  .client-details {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .client-name {
      font-size: 15px;
      font-weight: 600;
      color: #1e293b;
      margin-bottom: 4px;
    }

    .client-status {
      display: flex;
      align-items: center;
      gap: 6px;

      .status-indicator {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background-color: #22c55e;
        box-shadow: 0 0 6px rgba(34, 197, 94, 0.4);
        animation: pulse-glow 2s infinite;
      }

      .status-text {
        font-size: 13px;
        color: #16a34a;
        font-weight: 500;
      }
    }
  }
}

.sensors-panel {
  .sensors-title {
    font-size: 14px;
    font-weight: 600;
    color: #374151;
    margin-bottom: 12px;
  }

  .sensors-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
  }
}

.sensor-card {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px;
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  transition: all 0.3s ease;

  &.sensor-active {
    // border-color: #22c55e;
    // background: linear-gradient(135deg, #DCFCE7 0%, #BBF7D0 100%);
    
    .sensor-icon-container {
      background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
      box-shadow: 0 3px 10px rgba(34, 197, 94, 0.25);
    }
  }

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 3px 12px rgba(0, 0, 0, 0.1);
  }

  .sensor-icon-container {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: linear-gradient(135deg, #f87171 0%, #dc2626 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    flex-shrink: 0;

    .sensor-icon {
      width: 18px;
      height: 18px;
      background-size: contain;
      background-repeat: no-repeat;
      background-position: center;

      &.imu-icon {
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z'/%3E%3C/svg%3E");
      }

      &.camera-icon {
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M9 2L7.17 4H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2h-3.17L15 2H9zm3 15c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5z'/%3E%3C/svg%3E");
      }
    }
  }

  .sensor-info {
    flex: 1;
    min-width: 0;

    .sensor-name {
      font-size: 13px;
      font-weight: 600;
      color: #374151;
      margin-bottom: 2px;
    }

         .sensor-status {
       font-size: 11px;
       color: #dc2626;
       font-weight: 500;

       &.status-active {
         color: #16a34a;
       }
     }
  }
}

@keyframes pulse-glow {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

.log-mode-switch {
  margin-right: 16px;
}

.dashboard-container {
  height: 100%;
  overflow: auto;
  
  /* 自定义滚动条样式 */
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: rgba(235, 238, 245, 0.3);
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: rgba(144, 147, 153, 0.4);
    border-radius: 3px;
    transition: background 0.3s ease;
    
    &:hover {
      background: rgba(144, 147, 153, 0.6);
    }
  }
}

.dashboard-panels {
  // display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: auto auto;
  gap: 12px;
  padding: 10px;
}

.sensor-panel {
  position: relative;
  background: #fff;
  border-radius: 4px;
  padding: 8px;
  margin-bottom: 8px;
  .trajectory-header {
    width: 300px;
  }
  
  /* 全屏相机模式 */
  &.fullscreen-camera {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw !important;
    height: 100vh !important;
    z-index: 9999;
    margin: 0;
    border-radius: 0;
    padding: 0;
    background: #000;
    
    .sensor-header {
      top: 20px;
      right: 20px;
      z-index: 10000;
    }
    
    .panel-content {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0;
    }
    
    .camera-container {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
    }
    
    .camera-status {
      position: absolute;
      top: 20px;
      left: 20px;
      z-index: 10000;
      background: rgba(0, 0, 0, 0.7);
      color: white;
      padding: 8px 12px;
      border-radius: 6px;
      display: flex;
      gap: 16px;
    }
    
    .camera-image {
      max-width: 100%;
      max-height: 100%;
      object-fit: contain;
      border-radius: 0;
    }
    
    .no-camera-feed {
      color: white;
      font-size: 18px;
    }
  }
}

/* 全屏模式下的浮动箭头按钮 */
.fullscreen-arrow-btn {
  position: fixed;
  top: 50%;
  left: 20px;
  transform: translateY(-50%);
  z-index: 10002;
  background: rgba(0, 0, 0, 0.7);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 0 6px 6px 0;
  padding: 12px 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  
  &:hover {
    background: rgba(0, 0, 0, 0.9);
    border-color: #409EFF;
    transform: translateY(-50%) translateX(5px);
  }
}

/* 箭头图标样式 */
.arrow-icon {
  width: 14px;
  height: 14px;
  display: inline-block;
  transition: all 0.3s ease;
  
  &.arrow-right {
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/></svg>');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
  }
  
  &.arrow-left {
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M15.41 16.59L10.83 12l4.58-4.59L14 6l-6 6 6 6 1.41-1.41z"/></svg>');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
  }
}

/* 全屏模式下的背景遮罩 */
.fullscreen-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(8px);
  z-index: 1000;
  animation: fadeIn 0.3s ease-out;
}

/* 全屏模式下隐藏左侧栏 */
.left-column.fullscreen-hidden {
  display: none;
}



@keyframes slideInLeft {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* 全屏模式下复用的侧边栏容器 */
.fullscreen-sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 350px;
  height: 100vh;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-right: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 4px 0 20px rgba(0, 0, 0, 0.15);
  z-index: 10001;
  animation: slideInLeft 0.3s ease-out;
  overflow-y: auto;
  
  .sidebar-container {
    height: 100%;
    display: flex;
    flex-direction: column;
    background: transparent;
    
    .sidebar-section {
      background: rgba(255, 255, 255, 0.9);
      margin: 8px;
      border-radius: 8px;
      border: 1px solid rgba(0, 0, 0, 0.05);
      
      &:first-child {
        margin-top: 20px;
      }
      
      &:last-child {
        margin-bottom: 20px;
      }
    }
  }
}

.sensor-header {
  position: absolute;
  z-index: 1000;
  top: 5px;
  right: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
  
  h3 {
    margin: 0;
    color: #303133;
    font-size: 16px;
  }
}

/* 全屏眼睛按钮样式 */
.fullscreen-eye-btn {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  padding: 6px 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  
  &:hover {
    background: #fff;
    border-color: #409EFF;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
  }
}

/* 眼睛图标样式 */
.eye-icon {
  width: 16px;
  height: 16px;
  display: inline-block;
  transition: all 0.3s ease;
  
  &.eye-closed {
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23606266"><path d="M12 9c1.66 0 3 1.34 3 3s-1.34 3-3 3-3-1.34-3-3 1.34-3 3-3m0-2C7 7 2.73 10.39 2 12c.73 1.61 5 5 10 5s9.27-3.39 10-5c-.73-1.61-5-5-10-5zm0 10c-1.66 0-3-1.34-3-3s1.34-3 3-3 3 1.34 3 3-1.34 3-3 3z"/><path d="M1 1l22 22-1.41 1.41L2.41 2.41z"/></svg>');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
  }
  
  &.eye-open {
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23409EFF"><path d="M12 4.5C7 4.5 2.73 7.61 2 12c.73 4.39 5 7.5 10 7.5s9.27-3.11 10-7.5c-.73-4.39-5-7.5-10-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z"/></svg>');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
  }
}

.status-tag {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  background: #f56c6c;
  color: white;

  &.connected {
    background: #67c23a;
  }
}

// 轨迹面板样式
.trajectory-panel {
  .sensor-header {
    .trajectory-controls {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .trajectory-btn {
      padding: 4px 8px;
      border: 1px solid #dcdfe6;
      border-radius: 4px;
      background: #fff;
      color: #606266;
      font-size: 12px;
      cursor: pointer;
      transition: all 0.3s;

      &:hover {
        background: #ecf5ff;
        border-color: #409EFF;
        color: #409EFF;
      }

      &.clear-btn {
        &:hover {
          background: #fef0f0;
          border-color: #f56c6c;
          color: #f56c6c;
        }
      }
    }
  }
}

.trajectory-chart {
  width: 100%;
  height: 240px;
  margin-bottom: 8px;
}

.trajectory-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-top: 1px solid #ebeef5;

  .value-item {
    display: flex;
    align-items: center;
    gap: 4px;

    .label {
      font-size: 12px;
      color: #909399;
    }

    .value {
      font-size: 12px;
      color: #303133;
      font-weight: 500;
    }
  }
}

// 轨迹面板样式 - 保持与其他模块一致
.trajectory-panel {
  .trajectory-controls {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .trajectory-btn {
    padding: 4px 8px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    background: #fff;
    color: #606266;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s;

    &:hover {
      background: #ecf5ff;
      border-color: #409EFF;
      color: #409EFF;
    }

    &.clear-btn {
      &:hover {
        background: #fef0f0;
        border-color: #f56c6c;
        color: #f56c6c;
      }
    }
  }
}

.trajectory-chart {
  width: 100%;
  height: 300px;
  margin-bottom: 12px;
}

// 轨迹信息样式 - 与其他模块保持一致
.trajectory-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-top: 1px solid #ebeef5;
  background: #f5f7fa;
  margin: 0 -16px -16px -16px;
  border-radius: 0 0 8px 8px;

  .value-item {
    display: flex;
    align-items: center;
    gap: 6px;

    .label {
      font-size: 12px;
      color: #909399;
      font-weight: 500;
    }

    .value {
      font-size: 12px;
      color: #303133;
      font-weight: 600;
    }
  }
}

.sensor-data {
  .data-section {
    margin-bottom: 12px;
    
    h4 {
      margin: 0 0 8px;
      color: #606266;
      font-size: 13px;
    }
  }
}

.data-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 8px;
}

.data-item {
  background: #f5f7fa;
  padding: 4px 8px;
  border-radius: 4px;
  
  .label {
    color: #909399;
    font-size: 11px;
    margin-right: 4px;
  }
  
  .value {
    color: #303133;
    font-family: monospace;
    font-size: 12px;
  }
}

.covariance-grid {
  display: flex;
   grid-template-columns: repeat(2, 1fr); 
  gap: 8px;
}

.covariance-item {
  background: #f5f7fa;
  padding: 8px 12px;
  border-radius: 4px;
  
  .label {
    display: block;
    color: #909399;
    font-size: 13px;
    margin-bottom: 4px;
  }
  
  .value {
    color: #303133;
    font-family: monospace;
    font-size: 12px;
    word-break: break-all;
  }
}

.camera-container {
  position: relative;
  width: 100%;
  height: 360px;
  background: #f5f7fa;
  border-radius: 6px;
  overflow: hidden;
  margin-bottom: 8px;
}

.camera-status {
  position: absolute;
  top: 10px;
  left: 10px;
  z-index: 10;
  background: rgba(0, 0, 0, 0.3);
  padding: 10px;
  border-radius: 8px;
  color: white;

  .value-item {
    display: flex;
    align-items: center;
    margin-bottom: 5px;
    
    &:last-child {
      margin-bottom: 0;
    }

    .label {
      margin-right: 8px;
      font-size: 14px;
      color: rgba(255, 255, 255, 0.8);
    }

    .value {
      font-size: 14px;
      font-weight: 500;
      color: white;
    }
  }
}

.camera-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.no-camera-feed {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #909399;
  
  .camera-icon {
    width: 48px;
    height: 48px;
    margin-bottom: 12px;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23909399"><path d="M17 10.5V7c0-.55-.45-1-1-1H4c-.55 0-1 .45-1 1v10c0 .55.45 1 1 1h12c.55 0 1-.45 1-1v-3.5l4 4v-11l-4 4z"/></svg>');
    background-size: contain;
    background-repeat: no-repeat;
  }
  
  span {
    font-size: 14px;
  }
}

// 采集信息卡片样式
.collection-info-form {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.info-label {
  font-size: 15px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

/* 重复的collector-display样式已在上方定义 */

.collector-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: linear-gradient(135deg, #409EFF 0%, #3a8ee6 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 6px rgba(64, 158, 255, 0.3);
}

.collector-icon {
  display: inline-block;
  width: 16px;
  height: 16px;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/></svg>');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.collector-name {
  font-size: 15px;
  font-weight: 600;
  color: #303133;
}

/* 重复的info-select样式已在上方定义 */

.info-status {
  display: flex;
  justify-content: center;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 6px 20px;
  background-color: #fef0f0;
  border-radius: 20px;
  border: 1px solid #fbc4c4;
  transition: all 0.3s ease;
  
  &.complete {
    background-color: #f0f9ff;
    border-color: #b3d8ff;
  }
}

.status-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: #f56c6c;
  
  &.active {
    background-color: #67c23a;
    animation: pulse 2s infinite;
  }
}

.status-text {
  font-size: 14px;
  font-weight: 500;
 color: #4B8BF4;
  
  .complete & {
    color: #67c23a;
  }
}

// Element Plus 下拉选择器样式优化
:deep(.el-select-dropdown) {
  border-radius: 10px;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
}

:deep(.el-select-dropdown__item) {
  padding: 10px 16px;
  font-size: 14px;
  
  &:hover {
    background-color: #f5f7fa;
  }
  
  &.selected {
    background-color: #ecf5ff;
    color: #409EFF;
    font-weight: 600;
  }
}

.status-indicator.status-running {
  background-color: #e6f9ec;
  border: 1px solid #b7ebc6;
}
.status-indicator.status-stopped {
  background-color: #fdeaea;
  border: 1px solid #fbc4c4;
}
.status-dot.active {
  background-color: #67c23a;
}
.status-dot {
  background-color: #f56c6c;
}
.status-text.status-active {
  color: #67c23a;
}
.status-text.status-inactive {
  color: #f56c6c;
}

.reconnect-status {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-top: 8px;
  margin-bottom: 16px;
  padding: 10px;
  background-color: #f0f9eb;
  border: 1px solid #e1f3d8;
  border-radius: 8px;
}

.reconnect-progress {
  width: 100%;
  height: 6px;
  background-color: #e1f3d8;
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 8px;
}

.reconnect-bar {
  height: 100%;
  background-color: #67c23a;
  transition: width 0.3s ease;
  animation: pulse-green 2s infinite;
}

.reconnect-text {
  font-size: 13px;
  color: #67c23a;
  font-weight: 500;
}

@keyframes pulse-green {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.6;
  }
}

.error-message-container {
  margin-top: 16px;
  padding: 12px 16px;
  background-color: #fef0f0;
  border-radius: 4px;
}

.error-message-item {
  display: flex;
  align-items: center;
  color: #f56c6c;
  font-size: 14px;
  line-height: 1.8;

  .error-icon {
    margin-right: 8px;
    font-size: 16px;
  }

  & + .error-message-item {
    margin-top: 8px;
  }
}

.info-status {
  padding: 16px;
  
  .status-indicator {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    
    .status-dot {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background-color: #F56C6C;
      margin-right: 8px;
      
      &.active {
        background-color: #67C23A;
      }
    }
    
    .status-text {
      font-size: 16px;
      font-weight: 500;
      color: #F56C6C;
    }
    
    &.complete {
      .status-text {
        color: #67C23A;
      }
    }
  }
}

.error-messages {
  // background-color: #FEF0F0;
  border-radius: 4px;
  padding: 0 12px;

}

.error-item {
  display: flex;
  align-items: center;
  color: #F56C6C;
  font-size: 14px;
  line-height: 1.6;
  
  i {
    font-size: 16px;
    flex-shrink: 0;
  }
}

.error-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: #F56C6C;
  color: white;
  margin-right: 8px;
  font-size: 12px;
  font-weight: bold;
  flex-shrink: 0;
}

/* 轨迹和IMU合并面板样式 */
.trajectory-imu-panel {
  .panel-content {
    padding: 0;
  }
}

.trajectory-imu-content {
  display: flex;
  gap: 16px;
  padding: 8px;
  min-height: 320px;
}

.trajectory-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
}

.imu-section {
  flex: 0 0 280px;
  display: flex;
  flex-direction: column;
  border-left: 1px solid #e5e7eb;
  padding-left: 12px;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.trajectory-chart {
  flex: 1;
  min-height: 300px;
  margin-bottom: 12px;
}

.trajectory-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 12px;
  background-color: #f9fafb;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
}

.sensor-data {
  flex: 1;
  overflow-y: auto;
  max-height: 280px;

  /* 自定义滚动条样式 */
  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(235, 238, 245, 0.3);
    border-radius: 2px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(144, 147, 153, 0.4);
    border-radius: 2px;

    &:hover {
      background: rgba(144, 147, 153, 0.6);
    }
  }
}

/* 新的采集任务样式 */

.task-info-item {
  display: flex;
  align-items: center;
  padding: 8px 10px;
  margin-bottom: 12px;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;

  .info-label {
    color: #6c757d;
    font-size: 15px;
    font-weight: 500;
  }

  .info-value {
    display: flex;
    align-items: center;
    // gap: 4px;
    color: #495057;
    font-size: 12px;
    font-weight: 600;

    i {
      width: 16px;
      height: 16px;
      background-size: contain;
      background-repeat: no-repeat;
      background-position: center;
    }

    .user-icon {
      background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23495057'%3E%3Cpath d='M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z'/%3E%3C/svg%3E");
    }

    .id-icon {
      background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23495057'%3E%3Cpath d='M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8l-6-6zm4 18H6V4h7v5h5v11z'/%3E%3C/svg%3E");
    }

    .copy-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 24px;
      height: 24px;
      border: none;
      border-radius: 4px;
      background: #4B8BF4;
      cursor: pointer;
      transition: all 0.2s ease;
      margin-left: 8px;
      flex-shrink: 0;
      
      &:hover {
        background: #3b7ae4;
        transform: translateY(-1px);
      }
      
      .copy-icon {
        width: 12px;
        height: 12px;
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z'/%3E%3C/svg%3E");
        background-size: contain;
        background-repeat: no-repeat;
        background-position: center;
      }
    }
  }

  // 采集ID特殊样式
  &.collection-id-item {
    .info-value {
      .collection-id-text {
        flex: 1;
        min-width: 0;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-family: 'Courier New', monospace;
        font-size: 12px;
        color: #374151;
        cursor: default;
        display: block;
      }
    }
  }
}

.task-select-item {
  margin-bottom: 16px;
}

.custom-mode-switch {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
  
  .switch-label {
    font-size: 13px;
    color: #495057;
    font-weight: 500;
  }
  
  .custom-switch {
    --el-switch-on-color: #4B8BF4;
    --el-switch-off-color: #dcdfe6;
  }
}

.custom-instruction-container {
  .custom-instruction-input {
    margin-bottom: 12px;
    
    :deep(.el-textarea__inner) {
      border-radius: 6px;
      border: 1px solid #dcdfe6;
      font-size: 14px;
      line-height: 1.5;
      
      &:focus {
        border-color: #4B8BF4;
        box-shadow: 0 0 0 2px rgba(75, 139, 244, 0.1);
      }
    }
  }
  
  .custom-instruction-actions {
    display: flex;
    gap: 8px;
    justify-content: flex-end;
    
    .custom-action-btn {
      padding: 6px 16px;
      border-radius: 4px;
      font-size: 13px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s ease;
      border: 1px solid;
      
      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
      }
      
      &.cancel-btn {
        background: #ffffff;
        color: #6c757d;
        border-color: #dee2e6;
        
        &:hover:not(:disabled) {
          background: #f8f9fa;
          border-color: #adb5bd;
        }
      }
      
      &.confirm-btn {
        background: #4B8BF4;
        color: white;
        border-color: #4B8BF4;
        
        &:hover:not(:disabled) {
          background: #3b7ae4;
          border-color: #3b7ae4;
        }
        
        &:disabled {
          background: #c7d2fe;
          border-color: #c7d2fe;
        }
      }
    }
  }
}

.task-select {
  width: 100%;

  :deep(.el-input__wrapper) {
    // background-color: #f8f9fa;
    border: 1px solid #cacfd1d8;
    border-radius: 2px;
    padding: 12px 16px;
    box-shadow: none;
    transition: all 0.3s ease;

    &:hover {
      border-color: #ced4da;
      background-color: #f1f3f4;
    }

    &.is-focus {
      border-color: #007bff;
      background-color: #ffffff;
      box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
    }
  }

  :deep(.el-input__inner) {
    color: #495057;
    font-size: 16px;
    font-weight: 500;

    &::placeholder {
      color: #6c757d;
      font-weight: normal;
    }
  }

  :deep(.el-input__prefix) {
    display: flex;
    align-items: center;
    margin-right: 8px;
  }

  :deep(.el-select__caret) {
    color: #6c757d;
    font-size: 14px;
  }

  :deep(.el-select-dropdown) {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    margin-top: 4px;
  }
}

.task-icon {
  width: 16px;
  height: 16px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;

  &.requirement-icon {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%236c757d'%3E%3Cpath d='M9 11H7v6h2v-6zm4 0h-2v6h2v-6zm4 0h-2v6h2v-6zm2-7h-3V2h-2v2H8V2H6v2H3c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 16H3V8h14v12z'/%3E%3C/svg%3E");
  }

  &.command-icon {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%236c757d'%3E%3Cpath d='M9.4 16.6L4.8 12l4.6-4.6L8 6l-6 6 6 6 1.4-1.4zm5.2 0L19.2 12l-4.6-4.6L16 6l6 6-6 6-1.4-1.4z'/%3E%3C/svg%3E");
  }
}

.task-status-indicator {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background-color: #e3f2fd;
  border: 1px solid #bbdefb;
  border-radius: 8px;
  color: #1976d2;
  font-size: 15px;
  font-weight: 500;

  .info-icon {
    width: 16px;
    height: 16px;
    margin-right: 8px;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%231976d2'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-6h2v6zm0-8h-2V7h2v2z'/%3E%3C/svg%3E");
    background-size: contain;
    background-repeat: no-repeat;
  }
}

/* 新的任务控制样式 */

.control-buttons {
  display: flex;
  gap: 12px;
  margin-bottom: 20px;
}

.control-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 14px 24px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;

  &.start-btn {
    background-color: #4B8BF4;
    color: white;

    &:hover:not(:disabled) {
      background-color: #3b7ae4;
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(75, 139, 244, 0.3);
    }

    &:disabled {
      background-color: rgba(75, 139, 244, 0.4);
      color: rgba(255, 255, 255, 0.7);
      cursor: not-allowed;
    }
  }

  &.stop-btn {
    background-color: #F87171;
    color: white;

    &:hover:not(:disabled) {
      background-color: #f56565;
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(248, 113, 113, 0.3);
    }

    &:disabled {
      background-color: rgba(248, 113, 113, 0.4);
      color: rgba(255, 255, 255, 0.7);
      cursor: not-allowed;
    }
  }
}

.btn-icon {
  width: 16px;
  height: 16px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;

  &.play-icon {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M8 5v14l11-7z'/%3E%3C/svg%3E");
  }

  &.stop-icon {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M6 6h12v12H6z'/%3E%3C/svg%3E");
  }

  &.loading-icon {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M12,4a8,8,0,0,1,7.89,6.7A1.53,1.53,0,0,0,21.38,12h0a1.5,1.5,0,0,0,1.48-1.75,11,11,0,0,0-21.72,0A1.5,1.5,0,0,0,2.62,12h0a1.53,1.53,0,0,0,1.49-1.3A8,8,0,0,1,12,4Z'/%3E%3C/svg%3E");
  }
}

/* 旋转动画 */
.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.status-info {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 16px;
}

.status-row {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 15px;

  .status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #dc3545;

    &.active {
      background-color: #28a745;
      animation: pulse-dot 2s infinite;
    }
  }

  .status-label {
    color: #6c757d;
    font-weight: 500;
  }

  .status-value {
    color: #495057;
    font-weight: 600;
  }

  .status-time {
    margin-left: auto;
    color: #6c757d;
    font-family: 'Courier New', monospace;
  }
}

@keyframes pulse-dot {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 响应式设计 */
@media (max-width: 1400px) {
  .trajectory-imu-content {
    flex-direction: column;
    gap: 16px;
  }

  .imu-section {
    flex: none;
    border-left: none;
    border-top: 1px solid #e5e7eb;
    padding-left: 0;
    padding-top: 16px;
  }

  .trajectory-chart {
    min-height: 250px;
  }

  .sensor-data {
    max-height: 200px;
  }

  .control-buttons {
    flex-direction: column;
    gap: 8px;
  }
}
</style>
