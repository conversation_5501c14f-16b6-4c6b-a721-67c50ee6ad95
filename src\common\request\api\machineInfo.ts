import { request } from '@/common/request/request'

const baseUrl = import.meta.env.VITE_API_BASE_URL

/**
 * 创建设备
 * @param machineData 设备数据
 */
function createMachineApi (machineData: any): Promise<any> {
  return request({
    method: 'POST',
    url: baseUrl+'/collector/machines/',
    data: machineData
  })
}

/**
 * 删除设备
 * @param machineId 设备ID
 */
function deleteMachineApi (machineId: string): Promise<any> {
  return request({
    method: 'DELETE',
    url: baseUrl + `/collector/machines/${machineId}/`,
    data: {}
  })
}

/**
 * 获取设备列表
 */
function getMachinesApi (): Promise<any> {
  return request({
    method: 'GET',
    url: baseUrl + '/collector/machines/',
    headers: {
      'Accept': 'application/json'
    }
  })
}

/**
 * 更新设备
 * @param machineId 设备ID
 * @param machineData 设备数据
 */
function updateMachineApi (machineId: string, machineData: any): Promise<any> {
  return request({
    method: 'PUT',
    url: baseUrl + `/collector/machines/${machineId}/`,
    data: machineData
  })
}

export {
  createMachineApi,
  deleteMachineApi,
  getMachinesApi,
  updateMachineApi
} 