<script setup lang="ts">
import { onMounted } from 'vue'
import { ElSelect, ElOption, ElTag } from 'element-plus'
import { UploadFilled } from '@element-plus/icons-vue'
import useTaskManage from './taskManage'

const { 
  // 响应式数据
  taskList, 
  requirementList, 
  commandList,
  pagination, 
  loading,
  requirementsLoading, 
  commandsLoading, 
  dialogVisible, 
  dialogTitle,
  dialogType, 
  uploadFileList, 
  taskFormRef, 
  uploadRef, 
  rules,
  searchKeyword, 
  taskForm,
  
  handleRequirementChange,
  handleCommandChange,
  
  // 文件处理函数
  beforeUpload,
  handleFileChange,
  handleRemove,
  
  openCreateDialog,
  openEditDialog,
  submitForm,
  
  // 删除和分页函数
  handleDelete,
  handleCurrentChange,
  handleSizeChange,
  handleSearch,
  
  // 初始化函数
  initTaskManage
} = useTaskManage()

// 页面加载时初始化
onMounted(() => {
  initTaskManage()
})
</script>

<template>
  <div class="task-manage-container">
    <div class="page-header">
      <div class="breadcrumb">
        <span>您当前的位置：</span>
        <span class="location">数据采集系统</span>
        <span class="separator">/</span>
        <span class="current">任务管理</span>
      </div>
    </div>
    
    <div class="page-title">
      <h1>任务管理</h1>
    </div>
    
    <div class="page-content">
      <!-- 操作栏 -->
      <div class="operation-bar">
        <div class="left-actions">
          <el-button type="primary" @click="openCreateDialog">
            新建任务
          </el-button>
        </div>
        
        <div class="right-actions">
          <el-input
            v-model="searchKeyword"
            placeholder="请输入需求名称或指令描述"
            clearable
            @keyup.enter="handleSearch"
            class="search-input"
          >
            <template #append>
              <el-button @click="handleSearch">
                搜索
              </el-button>
            </template>
          </el-input>
        </div>
      </div>
      
      <!-- 任务表格 -->
      <el-table
        :data="taskList"
        style="width: 100%"
        border
        :loading="loading"
        max-height="500"
      >
        <el-table-column
          type="index"
          label="序号"
          width="80"
          align="center"
        />
        
        <el-table-column
          prop="id"
          label="任务ID"
          min-width="80"
        >
          <template #default="scope">
            {{ scope.row.id || '暂无' }}
          </template>
        </el-table-column>
        
        <el-table-column
          prop="requirement_title"
          label="需求名称"
          min-width="150"
        >
          <template #default="scope">
            <template v-if="Array.isArray(scope.row.requirement_titles)">
              <el-tag 
                v-for="title in scope.row.requirement_titles" 
                :key="title"
                style="margin-right: 5px; margin-bottom: 5px;"
                size="small"
              >
                {{ title }}
              </el-tag>
            </template>
            <template v-else>
              {{ scope.row.requirement_title || '暂无' }}
            </template>
          </template>
        </el-table-column>
        
        <el-table-column
          prop="command_description"
          label="指令描述"
          min-width="200"
          show-overflow-tooltip
        >
          <template #default="scope">
            <template v-if="Array.isArray(scope.row.command_descriptions)">
              <el-tag 
                v-for="desc in scope.row.command_descriptions" 
                :key="desc"
                style="margin-right: 5px; margin-bottom: 5px;"
                size="small"
                type="success"
              >
                {{ desc }}
              </el-tag>
            </template>
            <template v-else>
              {{ scope.row.command_description || '暂无' }}
            </template>
          </template>
        </el-table-column>
        
        <el-table-column
          prop="target"
          label="目标数量"
          min-width="100"
          align="center"
        >
          <template #default="scope">
            {{ scope.row.target || '0' }}
          </template>
        </el-table-column>
        
        <el-table-column
          prop="deadline"
          label="截止时间"
          min-width="160"
        >
          <template #default="scope">
            {{ scope.row.deadline || '暂无' }}
          </template>
        </el-table-column>
        
        <el-table-column
          label="操作"
          width="150"
          fixed="right"
        >
          <template #default="scope">
            <el-button
              type="primary"
              link
              @click="openEditDialog(scope.row)"
            >
              编辑
            </el-button>
            <el-button
              type="danger"
              link
              @click="handleDelete(scope.row.id)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          background
        />
      </div>
    </div>
    
    <!-- 任务表单对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="500px"
      destroy-on-close
    >
      <el-form
        ref="taskFormRef"
        :model="taskForm"
        :rules="rules"
        label-width="100px"
        label-position="right"
      >
        <el-form-item label="需求名称" prop="requirement_titles">
          <el-select 
            v-model="taskForm.requirement_titles" 
            placeholder="请选择需求" 
            style="width: 100%"
            @change="handleRequirementChange"
            :loading="requirementsLoading"
            multiple
          >
            <el-option
              v-for="item in requirementList"
              :key="item.id"
              :label="item.title"
              :value="item.title"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="需求ID" prop="requirement_ids">
          <el-input v-model="taskForm.requirement_ids" placeholder="需求ID" disabled />
        </el-form-item>
        
        <!-- 新建任务时显示文件上传 -->
        <el-form-item v-if="dialogType === 'create'" label="指令文件" prop="command_file">
          <div class="upload-content">
            <!-- 上传区域 -->
            <el-upload
              ref="uploadRef"
              :file-list="uploadFileList"
              :before-upload="beforeUpload"
              :on-change="handleFileChange"
              :on-remove="handleRemove"
              :auto-upload="false"
              accept=".csv"
              drag
              multiple
              :limit="1"
              class="form-upload"
            >
       
              <div class="upload-inner">
                <el-icon class="upload-icon"><UploadFilled /></el-icon>
                <div class="upload-text">点击或拖拽CSV文件到此处</div>
              </div>
            </el-upload>
            
            <!-- 上传说明 -->
            <div class="upload-tips">
              <div class="tip-item">支持CSV格式，不超过10MB; CSV第一列为指令描述</div>
            </div>
          </div>
        </el-form-item>
        
        <!-- 编辑任务时显示指令选择 -->
        <template v-if="dialogType === 'edit'">
          <el-form-item label="指令描述" prop="command_descriptions">
            <el-select 
              v-model="taskForm.command_descriptions" 
              placeholder="请选择指令" 
              style="width: 100%"
              @change="handleCommandChange"
              :loading="commandsLoading"
              multiple
            >
              <el-option
                v-for="item in commandList"
                :key="item.id"
                :label="item.description"
                :value="item.description"
              />
            </el-select>
          </el-form-item>
          
          <el-form-item label="指令ID" prop="command_ids">
            <el-input v-model="taskForm.command_ids" placeholder="指令ID" disabled />
          </el-form-item>
        </template>
        
        <el-form-item label="目标数量" prop="target">
          <el-input 
            v-model.number="taskForm.target" 
            type="number" 
            placeholder="请输入目标数量" 
          />
        </el-form-item>
        
        <el-form-item label="截止时间" prop="deadline">
          <el-date-picker
            v-model="taskForm.deadline"
            type="datetime"
            placeholder="请选择截止时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            :clearable="true"
            style="width: 100%"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.task-manage-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.breadcrumb {
  font-size: 14px;
  color: #606266;
  
  .location {
    color: #909399;
  }
  
  .separator {
    margin: 0 8px;
    color: #C0C4CC;
  }
  
  .current {
    color: #303133;
    font-weight: 500;
  }
}

.page-title {
  margin-bottom: 20px;
  
  h1 {
    font-size: 24px;
    color: #303133;
    margin: 0;
    font-weight: 600;
  }
}

.page-content {
  background-color: #fff;
  border-radius: 4px;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.operation-bar {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  
  .search-input {
    width: 300px;
  }
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.upload-content {
  width: 100%;
  
  .form-upload {
    width: 100%;
    
    :deep(.el-upload) {
      width: 100%;
    }
    
    :deep(.el-upload-dragger) {
      width: 100%;
      height: 32px;
      border: 1px solid #dcdfe6;
      border-radius: 4px;
      background-color: #ffffff;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
      box-sizing: border-box;
      font-size: 14px;
      
      &:hover {
        border-color: #c0c4cc;
      }
      
      &:focus {
        border-color: #409eff;
        outline: none;
      }
    }
  }
  
  .upload-inner {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 0 11px;
    width: 100%;
    height: 100%;
    
    .upload-icon {
      font-size: 16px;
      color: #c0c4cc;
      margin-right: 8px;
      flex-shrink: 0;
    }
    
    .upload-text {
      color: #a8abb2;
      font-size: 14px;
      line-height: 32px;
    }
  }
  
  .upload-tips {
    margin-top: 8px;
    padding-left: 2px;
    
    .tip-item {
      font-size: 11px;
      color: #c0c4cc;
      line-height: 1.4;
      margin-bottom: 2px;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
  
  :deep(.el-upload-list) {
    margin-top: 4px;
  }
  
  :deep(.el-upload-list__item) {
    margin-top: 4px;
    padding: 4px 8px;
    border-radius: 4px;
    background-color: #f5f7fa;
    border: 1px solid #e4e7ed;
    font-size: 13px;
    line-height: 1.4;
  }
  
  :deep(.el-upload-list__item-name) {
    color: #606266;
  }
  
  :deep(.el-upload-list__item-status-label) {
    display: none;
  }
}
</style> 